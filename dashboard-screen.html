<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧校园管理系统 - 数据大屏</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 150, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 0, 150, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 10s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes backgroundPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }

        .dashboard-container {
            display: grid;
            grid-template-areas: 
                "header header header"
                "left center right"
                "footer footer footer";
            grid-template-rows: 80px 1fr 100px;
            grid-template-columns: 1fr 2fr 1fr;
            height: 100vh;
            gap: 15px;
            padding: 15px;
        }

        /* 头部区域 */
        .dashboard-header {
            grid-area: header;
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.2) 0%, rgba(0, 255, 150, 0.2) 100%);
            border: 1px solid rgba(0, 150, 255, 0.3);
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 150, 255, 0.2);
        }

        .system-title {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .header-info {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .current-time {
            font-size: 18px;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .weather-info {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 16px;
            color: #00ff88;
        }

        /* 左侧区域 */
        .dashboard-left {
            grid-area: left;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* 中央区域 */
        .dashboard-center {
            grid-area: center;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* 右侧区域 */
        .dashboard-right {
            grid-area: right;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* 底部区域 */
        .dashboard-footer {
            grid-area: footer;
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
        }

        /* 数据卡片基础样式 */
        .data-card {
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 255, 150, 0.1) 100%);
            border: 1px solid rgba(0, 150, 255, 0.3);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 150, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 150, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.5);
        }

        .card-title {
            font-size: 16px;
            color: #00d4ff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-title i {
            font-size: 18px;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #8cc8ff;
            opacity: 0.8;
        }

        .stat-change {
            font-size: 12px;
            margin-top: 5px;
        }

        .stat-change.positive {
            color: #00ff88;
        }

        .stat-change.negative {
            color: #ff4757;
        }

        /* 图表容器 */
        .chart-container {
            height: 200px;
            position: relative;
        }

        .chart-container.large {
            height: 300px;
        }

        .chart-container.small {
            height: 150px;
        }

        /* 进度条样式 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #00ff88);
            border-radius: 4px;
            transition: width 1s ease;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-online {
            background: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .status-warning {
            background: #ffa502;
            box-shadow: 0 0 10px rgba(255, 165, 2, 0.5);
        }

        .status-error {
            background: #ff4757;
            box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 数据列表 */
        .data-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .data-list li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 14px;
        }

        .data-list li:last-child {
            border-bottom: none;
        }

        .data-list .label {
            color: #8cc8ff;
        }

        .data-list .value {
            color: #ffffff;
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 1920px) {
            .dashboard-container {
                grid-template-columns: 350px 1fr 350px;
            }
        }

        @media (max-width: 1366px) {
            .dashboard-container {
                grid-template-columns: 300px 1fr 300px;
                gap: 10px;
                padding: 10px;
            }
            
            .system-title {
                font-size: 24px;
            }
            
            .stat-number {
                font-size: 28px;
            }
        }

        /* 全屏按钮 */
        .fullscreen-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 150, 255, 0.2);
            border: 1px solid rgba(0, 150, 255, 0.3);
            color: #00d4ff;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .fullscreen-btn:hover {
            background: rgba(0, 150, 255, 0.3);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <button class="fullscreen-btn" onclick="toggleFullscreen()">
        <i class="fas fa-expand"></i>
    </button>

    <div class="dashboard-container">
        <!-- 头部区域 -->
        <div class="dashboard-header">
            <div class="system-title">
                <i class="fas fa-graduation-cap"></i>
                智慧校园管理系统 - 数据大屏
            </div>
            <div class="header-info">
                <div class="current-time" id="currentTime">
                    2024年06月25日 14:30:25
                </div>
                <div class="weather-info">
                    <i class="fas fa-cloud-sun"></i>
                    <span>晴转多云 22°C</span>
                </div>
            </div>
        </div>

        <!-- 左侧区域 -->
        <div class="dashboard-left">
            <!-- 学生统计 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-user-graduate"></i>
                    学生信息统计
                </div>
                <div class="stat-number" id="totalStudents">12,456</div>
                <div class="stat-label">在校学生总数</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> 较上月增长 2.3%
                </div>
                <ul class="data-list" style="margin-top: 15px;">
                    <li>
                        <span class="label">本科生</span>
                        <span class="value">9,234</span>
                    </li>
                    <li>
                        <span class="label">研究生</span>
                        <span class="value">2,890</span>
                    </li>
                    <li>
                        <span class="label">博士生</span>
                        <span class="value">332</span>
                    </li>
                </ul>
            </div>

            <!-- 考勤统计图表 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-clock"></i>
                    今日考勤统计
                </div>
                <div class="chart-container">
                    <canvas id="attendanceChart"></canvas>
                </div>
            </div>

            <!-- 图书馆数据 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-book"></i>
                    图书资源统计
                </div>
                <ul class="data-list">
                    <li>
                        <span class="label">馆藏图书</span>
                        <span class="value">856,234</span>
                    </li>
                    <li>
                        <span class="label">今日借阅</span>
                        <span class="value">1,234</span>
                    </li>
                    <li>
                        <span class="label">电子资源</span>
                        <span class="value">45,678</span>
                    </li>
                    <li>
                        <span class="label">在线访问</span>
                        <span class="value">8,901</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 中央区域 -->
        <div class="dashboard-center">
            <!-- 核心数据展示 -->
            <div class="data-card" style="flex: 1;">
                <div class="card-title">
                    <i class="fas fa-chart-line"></i>
                    校园核心数据概览
                </div>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-top: 20px;">
                    <div style="text-align: center;">
                        <div class="stat-number" style="font-size: 24px; color: #00d4ff;">96.8%</div>
                        <div class="stat-label">今日出勤率</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 96.8%;"></div>
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <div class="stat-number" style="font-size: 24px; color: #00ff88;">342</div>
                        <div class="stat-label">开设课程</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%;"></div>
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <div class="stat-number" style="font-size: 24px; color: #ffa502;">89.5%</div>
                        <div class="stat-label">教室使用率</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 89.5%;"></div>
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <div class="stat-number" style="font-size: 24px; color: #ff6b9d;">1,234</div>
                        <div class="stat-label">在线学习</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 78%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 成绩分布图表 -->
                <div class="chart-container large" style="margin-top: 30px;">
                    <canvas id="gradeDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 右侧区域 -->
        <div class="dashboard-right">
            <!-- 教师统计 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-chalkboard-teacher"></i>
                    教师信息统计
                </div>
                <div class="stat-number" id="totalTeachers">856</div>
                <div class="stat-label">在职教师总数</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> 较上月增长 1.2%
                </div>
                <ul class="data-list" style="margin-top: 15px;">
                    <li>
                        <span class="label">教授</span>
                        <span class="value">156</span>
                    </li>
                    <li>
                        <span class="label">副教授</span>
                        <span class="value">234</span>
                    </li>
                    <li>
                        <span class="label">讲师</span>
                        <span class="value">466</span>
                    </li>
                </ul>
            </div>

            <!-- 教学资源使用率 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-laptop"></i>
                    智慧课堂使用率
                </div>
                <div class="chart-container">
                    <canvas id="classroomUsageChart"></canvas>
                </div>
            </div>

            <!-- 校园服务统计 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-university"></i>
                    校园服务统计
                </div>
                <ul class="data-list">
                    <li>
                        <span class="label">一卡通消费</span>
                        <span class="value">¥45,678</span>
                    </li>
                    <li>
                        <span class="label">食堂就餐</span>
                        <span class="value">8,901人次</span>
                    </li>
                    <li>
                        <span class="label">宿舍入住率</span>
                        <span class="value">98.5%</span>
                    </li>
                    <li>
                        <span class="label">报修工单</span>
                        <span class="value">23个</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 底部区域 -->
        <div class="dashboard-footer">
            <!-- 系统状态1 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-server"></i>
                    教务系统
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <span class="status-indicator status-online"></span>
                    <span style="color: #00ff88;">正常运行</span>
                </div>
                <div class="stat-label" style="margin-top: 5px;">响应时间: 45ms</div>
            </div>

            <!-- 系统状态2 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-users"></i>
                    学生管理
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <span class="status-indicator status-online"></span>
                    <span style="color: #00ff88;">正常运行</span>
                </div>
                <div class="stat-label" style="margin-top: 5px;">响应时间: 32ms</div>
            </div>

            <!-- 系统状态3 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-shield-alt"></i>
                    安防系统
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <span class="status-indicator status-warning"></span>
                    <span style="color: #ffa502;">维护中</span>
                </div>
                <div class="stat-label" style="margin-top: 5px;">预计完成: 16:00</div>
            </div>

            <!-- 系统状态4 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-book"></i>
                    图书系统
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <span class="status-indicator status-online"></span>
                    <span style="color: #00ff88;">正常运行</span>
                </div>
                <div class="stat-label" style="margin-top: 5px;">响应时间: 28ms</div>
            </div>

            <!-- 系统状态5 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-network-wired"></i>
                    网络状态
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <span class="status-indicator status-online"></span>
                    <span style="color: #00ff88;">正常运行</span>
                </div>
                <div class="stat-label" style="margin-top: 5px;">带宽使用: 65%</div>
            </div>

            <!-- 系统状态6 -->
            <div class="data-card">
                <div class="card-title">
                    <i class="fas fa-database"></i>
                    数据库
                </div>
                <div style="display: flex; align-items: center; margin-top: 10px;">
                    <span class="status-indicator status-online"></span>
                    <span style="color: #00ff88;">正常运行</span>
                </div>
                <div class="stat-label" style="margin-top: 5px;">连接数: 234/500</div>
            </div>
        </div>
    </div>

    <script>
        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 初始化图表
        function initCharts() {
            // 考勤统计图表
            const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
            new Chart(attendanceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['正常出勤', '迟到', '请假', '缺勤'],
                    datasets: [{
                        data: [85, 8, 5, 2],
                        backgroundColor: [
                            'rgba(0, 255, 136, 0.8)',
                            'rgba(255, 165, 2, 0.8)',
                            'rgba(0, 212, 255, 0.8)',
                            'rgba(255, 71, 87, 0.8)'
                        ],
                        borderColor: [
                            'rgba(0, 255, 136, 1)',
                            'rgba(255, 165, 2, 1)',
                            'rgba(0, 212, 255, 1)',
                            'rgba(255, 71, 87, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#ffffff',
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });

            // 成绩分布图表
            const gradeCtx = document.getElementById('gradeDistributionChart').getContext('2d');
            new Chart(gradeCtx, {
                type: 'bar',
                data: {
                    labels: ['优秀(90-100)', '良好(80-89)', '中等(70-79)', '及格(60-69)', '不及格(<60)'],
                    datasets: [{
                        label: '学生人数',
                        data: [1234, 2345, 3456, 2234, 234],
                        backgroundColor: [
                            'rgba(0, 255, 136, 0.8)',
                            'rgba(0, 212, 255, 0.8)',
                            'rgba(255, 165, 2, 0.8)',
                            'rgba(255, 107, 157, 0.8)',
                            'rgba(255, 71, 87, 0.8)'
                        ],
                        borderColor: [
                            'rgba(0, 255, 136, 1)',
                            'rgba(0, 212, 255, 1)',
                            'rgba(255, 165, 2, 1)',
                            'rgba(255, 107, 157, 1)',
                            'rgba(255, 71, 87, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#ffffff',
                                font: {
                                    size: 10
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });

            // 智慧课堂使用率图表
            const classroomCtx = document.getElementById('classroomUsageChart').getContext('2d');
            new Chart(classroomCtx, {
                type: 'line',
                data: {
                    labels: ['8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00'],
                    datasets: [{
                        label: '使用率',
                        data: [45, 78, 65, 89, 92, 76, 34],
                        borderColor: 'rgba(0, 212, 255, 1)',
                        backgroundColor: 'rgba(0, 212, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                color: '#ffffff',
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // 数据更新动画
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const finalValue = parseInt(number.textContent.replace(/[^\d]/g, ''));
                let currentValue = 0;
                const increment = finalValue / 50;
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }

                    if (number.textContent.includes('%')) {
                        number.textContent = currentValue.toFixed(1) + '%';
                    } else if (number.textContent.includes(',')) {
                        number.textContent = Math.floor(currentValue).toLocaleString();
                    } else {
                        number.textContent = Math.floor(currentValue);
                    }
                }, 50);
            });
        }

        // 模拟实时数据更新
        function updateRealTimeData() {
            // 更新学生总数
            const studentCount = document.getElementById('totalStudents');
            const currentStudents = parseInt(studentCount.textContent.replace(/,/g, ''));
            const newStudents = currentStudents + Math.floor(Math.random() * 5) - 2;
            studentCount.textContent = Math.max(0, newStudents).toLocaleString();

            // 更新教师总数
            const teacherCount = document.getElementById('totalTeachers');
            const currentTeachers = parseInt(teacherCount.textContent);
            const newTeachers = currentTeachers + Math.floor(Math.random() * 3) - 1;
            teacherCount.textContent = Math.max(0, newTeachers);

            // 更新进度条
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const currentWidth = parseFloat(bar.style.width);
                const change = (Math.random() - 0.5) * 2;
                const newWidth = Math.max(0, Math.min(100, currentWidth + change));
                bar.style.width = newWidth + '%';

                // 更新对应的数字
                const parentCard = bar.closest('div').parentElement;
                const statNumber = parentCard.querySelector('.stat-number');
                if (statNumber && statNumber.textContent.includes('%')) {
                    statNumber.textContent = newWidth.toFixed(1) + '%';
                }
            });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);

            setTimeout(() => {
                initCharts();
                animateNumbers();
            }, 500);

            // 每30秒更新一次数据
            setInterval(updateRealTimeData, 30000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
    </script>
</body>
</html>
