# 智慧校园管理系统

一个现代化的智慧校园管理系统前端原型，提供完整的校园管理功能和美观的用户界面。

## 🌟 项目特色

- **现代化UI设计**：采用渐变色彩、圆角设计、阴影效果等现代化UI元素
- **响应式布局**：适配桌面和移动设备，提供良好的用户体验
- **模块化架构**：功能模块独立，便于维护和扩展
- **统一导航系统**：左侧导航栏统一管理，支持多级菜单
- **交互式界面**：丰富的交互效果和动画，提升用户体验

## 📋 功能模块

### 🏠 首页
- 数据看板展示
- 快捷操作入口
- 实时统计信息
- 最近活动记录

### 📚 教学管理
- **教学资源管理**：智能排课、教室管理、教材管理
- **教务综合管理**：学籍管理、成绩管理、考务管理
- **教学评价系统**：学生评教、教师互评、评教报告

### 👨‍🎓 学生管理
- **学生信息中心**：基本信息、学业信息、家庭信息、活动记录
- **学生考勤系统**：多种签到方式、考勤记录、请假申请
- **心理健康咨询**：心理测评、咨询预约、心情记录

### 👨‍🏫 教师管理
- **人事管理**：教师信息、聘任管理、职称评定、证书管理、培训记录
- **教师考勤排班**：签到系统、排班表、假勤管理、绩效统计、节假日管理

### 🏫 校园服务
- **一卡通系统**：余额查询、充值服务、消费记录、校园服务
- **安防监控系统**：视频监控、门禁管理、访客管理、巡逻记录
- **后勤服务系统**：报修工单、资产管理、能源监控、环境控制
- **宿舍管理系统**：房间分配、住宿管理、访客登记、电控系统、智能门锁

### 📚 图书资源
- **图书资源中心**：图书检索、借还管理、读者管理、在线预约、电子图书馆、阅读统计

### 💻 智慧课堂
- **智慧课堂系统**：智能终端、在线作业、混合教学、课堂互动、资源共享、学习分析

### 📢 信息发布
- **信息发布门户**：内容管理、新闻公告、通知推送、多渠道发布、活动信息、信息统计

### 📊 数据分析
- **数据分析中心**：学业预警、学生画像、AI智能分析、教学质量分析、校园运营分析

### ⚙️ 系统管理
- **系统设置**：用户权限管理、系统配置、数据备份、安全设置、系统监控、日志管理

## 🚀 快速开始

### 环境要求
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 本地Web服务器（可选）

### 运行方式

#### 方式一：直接打开
直接用浏览器打开 `index.html` 文件即可

#### 方式二：本地服务器
```bash
# 使用Python启动本地服务器
python3 -m http.server 8000

# 或使用Node.js
npx http-server

# 然后访问 http://localhost:8000
```

## 📁 项目结构

```
智慧校园管理系统/
├── index.html                    # 主入口页面
├── assets/                       # 静态资源
│   ├── css/
│   │   └── common.css            # 公共样式文件
│   └── js/
│       └── common.js             # 公共JavaScript文件
├── teaching-resources.html       # 教学资源管理
├── academic-affairs.html         # 教务综合管理
├── teaching-evaluation.html      # 教学评价系统
├── student-info.html             # 学生信息中心
├── student-attendance.html       # 学生考勤系统
├── psychological-health.html     # 心理健康咨询
├── hr-management.html            # 人事管理
├── teacher-attendance.html       # 教师考勤排班
├── teacher-development.html      # 教师发展档案
├── card-system.html              # 一卡通系统
├── security-system.html          # 安防监控系统
├── logistics-system.html         # 后勤服务系统
├── dormitory-system.html         # 宿舍管理系统
├── library-system.html           # 图书资源中心
├── smart-classroom.html          # 智慧课堂系统
├── information-portal.html       # 信息发布门户
├── data-analytics.html           # 数据分析中心
├── system-settings.html          # 系统设置
└── README.md                     # 项目说明文档
```

## 🎨 设计特色

### 色彩方案
- 主色调：蓝色渐变 (#667eea → #764ba2)
- 辅助色：绿色 (#2ecc71)、橙色 (#f39c12)、红色 (#e74c3c)
- 背景色：浅色渐变，营造清新感

### UI组件
- **卡片式布局**：信息模块化展示
- **渐变按钮**：现代化的交互元素
- **图标系统**：使用FontAwesome图标库
- **动画效果**：悬停、点击等交互动画

### 响应式设计
- 桌面端：多列网格布局
- 移动端：单列堆叠布局
- 自适应导航：移动端可收缩侧边栏

## 🔧 技术栈

- **HTML5**：语义化标签，提供良好的结构
- **CSS3**：现代化样式，包括Grid、Flexbox、渐变、动画
- **JavaScript ES6+**：模块化代码，现代语法
- **FontAwesome**：图标库
- **响应式设计**：适配多种设备

## 📱 页面展示

### 主要页面
1. **首页**：数据看板和快捷操作
2. **教学资源管理**：排课系统、教室管理、教材管理
3. **教务综合管理**：学籍管理、成绩管理、考务管理
4. **教学评价系统**：学生评教、教师互评、评教报告
5. **学生信息中心**：完整的学生档案管理
6. **学生考勤系统**：多种签到方式和记录查询
7. **心理健康咨询**：测评系统和咨询预约
8. **人事管理**：教师信息、聘任管理、职称评定
9. **教师考勤排班**：签到系统、排班表、假勤管理
10. **教师发展档案**：教学活动、科研成果、评优记录、继续教育
11. **一卡通系统**：校园卡管理和消费服务
12. **安防监控系统**：视频监控、门禁管理、访客管理
13. **后勤服务系统**：报修工单、资产管理、能源监控
14. **宿舍管理系统**：房间分配、访客登记、智能门锁
15. **图书资源中心**：图书检索、借还管理、电子图书馆
16. **智慧课堂系统**：智能终端、在线作业、混合教学
17. **信息发布门户**：内容管理、新闻公告、通知推送
18. **数据分析中心**：学业预警、学生画像、AI智能分析
19. **系统设置**：用户权限、系统配置、数据备份

### 特色功能
- 实时时间显示
- 动态数据更新
- 交互式表单
- 多选项卡界面
- 模态对话框
- 消息提示系统

## 🌐 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📄 开源协议

本项目采用 MIT 协议开源，详见 LICENSE 文件。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/smartcampus/management-system

---

**智慧校园管理系统** - 让校园管理更智能、更高效！
