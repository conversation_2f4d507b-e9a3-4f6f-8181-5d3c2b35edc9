<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后勤服务系统 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .logistics-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .logistics-stat {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .logistics-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .service-icon {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .service-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .service-desc {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .service-btn {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .service-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(243,156,18,0.3);
        }

        .repair-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .repair-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .repair-table th {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .repair-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .repair-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .priority-high {
            background: #f8d7da;
            color: #721c24;
        }

        .priority-medium {
            background: #fff3cd;
            color: #856404;
        }

        .priority-low {
            background: #d4edda;
            color: #155724;
        }

        .asset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .asset-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .asset-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .asset-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .asset-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .asset-normal {
            background: #d4edda;
            color: #155724;
        }

        .asset-maintenance {
            background: #fff3cd;
            color: #856404;
        }

        .asset-broken {
            background: #f8d7da;
            color: #721c24;
        }

        .asset-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .asset-detail {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .detail-label {
            color: #666;
        }

        .detail-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .energy-monitor {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .energy-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .energy-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .energy-icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #f39c12;
        }

        .energy-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .energy-label {
            font-size: 12px;
            color: #666;
        }

        .energy-trend {
            font-size: 12px;
            margin-top: 5px;
        }

        .trend-up {
            color: #e74c3c;
        }

        .trend-down {
            color: #2ecc71;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-tools"></i> 后勤服务系统
                </h1>

                <!-- 后勤统计 -->
                <div class="logistics-dashboard">
                    <div class="logistics-stat">
                        <div class="stat-icon">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">待处理工单</div>
                    </div>
                    <div class="logistics-stat">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-number">1,256</div>
                        <div class="stat-label">资产设备</div>
                    </div>
                    <div class="logistics-stat">
                        <div class="stat-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="stat-number">85.6%</div>
                        <div class="stat-label">能源效率</div>
                    </div>
                    <div class="logistics-stat">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">45</div>
                        <div class="stat-label">后勤人员</div>
                    </div>
                </div>

                <!-- 服务入口 -->
                <div class="service-grid">
                    <div class="service-card" onclick="openRepairService()">
                        <div class="service-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="service-title">报修服务</div>
                        <div class="service-desc">设备故障报修、维修进度查询</div>
                        <button class="service-btn">立即报修</button>
                    </div>

                    <div class="service-card" onclick="openAssetManagement()">
                        <div class="service-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="service-title">资产管理</div>
                        <div class="service-desc">设备资产登记、盘点、维护</div>
                        <button class="service-btn">资产查询</button>
                    </div>

                    <div class="service-card" onclick="openEnergyMonitor()">
                        <div class="service-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="service-title">能源监控</div>
                        <div class="service-desc">水电气使用监控、节能分析</div>
                        <button class="service-btn">查看监控</button>
                    </div>

                    <div class="service-card" onclick="openEnvironmentControl()">
                        <div class="service-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="service-title">环境控制</div>
                        <div class="service-desc">温湿度控制、空气质量监测</div>
                        <button class="service-btn">环境监测</button>
                    </div>

                    <div class="service-card" onclick="openCleaningService()">
                        <div class="service-icon">
                            <i class="fas fa-broom"></i>
                        </div>
                        <div class="service-title">清洁服务</div>
                        <div class="service-desc">清洁任务安排、质量检查</div>
                        <button class="service-btn">清洁管理</button>
                    </div>

                    <div class="service-card" onclick="openSecurityService()">
                        <div class="service-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="service-title">安保服务</div>
                        <div class="service-desc">安全巡逻、应急响应</div>
                        <button class="service-btn">安保管理</button>
                    </div>
                </div>

                <!-- 报修工单 -->
                <div class="repair-form" id="repair-section" style="display: none;">
                    <h3 style="margin-bottom: 20px;">
                        <i class="fas fa-plus-circle"></i> 设备报修申请
                    </h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">报修地点</label>
                            <select class="form-control">
                                <option>教学楼A座</option>
                                <option>教学楼B座</option>
                                <option>实验楼C座</option>
                                <option>图书馆</option>
                                <option>学生宿舍</option>
                                <option>食堂</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">具体位置</label>
                            <input type="text" class="form-control" placeholder="如：101教室、3楼走廊等">
                        </div>
                        <div class="form-group">
                            <label class="form-label">故障类型</label>
                            <select class="form-control">
                                <option>电路故障</option>
                                <option>水管漏水</option>
                                <option>空调故障</option>
                                <option>门窗损坏</option>
                                <option>投影设备</option>
                                <option>网络故障</option>
                                <option>其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">紧急程度</label>
                            <select class="form-control">
                                <option>紧急</option>
                                <option>一般</option>
                                <option>不急</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">故障描述</label>
                        <textarea class="form-control" rows="4" placeholder="请详细描述故障现象和影响..."></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">联系方式</label>
                        <input type="tel" class="form-control" placeholder="请输入您的联系电话">
                    </div>
                    <div style="text-align: center; margin-top: 30px;">
                        <button class="btn btn-success" style="padding: 12px 40px;">
                            <i class="fas fa-paper-plane"></i> 提交报修
                        </button>
                        <button class="btn btn-secondary" style="padding: 12px 40px; margin-left: 10px;" onclick="hideRepairForm()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </div>

                <!-- 工单列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近报修工单</h3>
                    </div>
                    <div class="card-body">
                        <table class="repair-table">
                            <thead>
                                <tr>
                                    <th>工单号</th>
                                    <th>报修地点</th>
                                    <th>故障类型</th>
                                    <th>紧急程度</th>
                                    <th>报修时间</th>
                                    <th>处理状态</th>
                                    <th>维修人员</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>WO202406001</td>
                                    <td>教学楼A座101</td>
                                    <td>投影设备故障</td>
                                    <td><span class="status-badge priority-high">紧急</span></td>
                                    <td>2024-06-25 09:30</td>
                                    <td><span class="status-badge status-processing">处理中</span></td>
                                    <td>李师傅</td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>WO202406002</td>
                                    <td>宿舍楼2号楼</td>
                                    <td>水管漏水</td>
                                    <td><span class="status-badge priority-medium">一般</span></td>
                                    <td>2024-06-25 08:15</td>
                                    <td><span class="status-badge status-completed">已完成</span></td>
                                    <td>王师傅</td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">评价</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>WO202406003</td>
                                    <td>图书馆3楼</td>
                                    <td>空调故障</td>
                                    <td><span class="status-badge priority-low">不急</span></td>
                                    <td>2024-06-24 16:45</td>
                                    <td><span class="status-badge status-pending">待处理</span></td>
                                    <td>-</td>
                                    <td>
                                        <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">分配</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 资产管理 -->
                <div class="card" id="asset-section" style="display: none;">
                    <div class="card-header">
                        <h3 class="card-title">设备资产管理</h3>
                    </div>
                    <div class="card-body">
                        <div class="asset-grid">
                            <div class="asset-card">
                                <div class="asset-header">
                                    <div class="asset-name">多媒体投影仪</div>
                                    <div class="asset-status asset-normal">正常</div>
                                </div>
                                <div class="asset-details">
                                    <div class="asset-detail">
                                        <span class="detail-label">资产编号</span>
                                        <span class="detail-value">AS001001</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">所在位置</span>
                                        <span class="detail-value">A101教室</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">购入日期</span>
                                        <span class="detail-value">2023-09-01</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">保修期</span>
                                        <span class="detail-value">2年</span>
                                    </div>
                                </div>
                            </div>

                            <div class="asset-card">
                                <div class="asset-header">
                                    <div class="asset-name">中央空调系统</div>
                                    <div class="asset-status asset-maintenance">维护中</div>
                                </div>
                                <div class="asset-details">
                                    <div class="asset-detail">
                                        <span class="detail-label">资产编号</span>
                                        <span class="detail-value">AS002001</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">所在位置</span>
                                        <span class="detail-value">教学楼A座</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">购入日期</span>
                                        <span class="detail-value">2022-06-15</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">保修期</span>
                                        <span class="detail-value">5年</span>
                                    </div>
                                </div>
                            </div>

                            <div class="asset-card">
                                <div class="asset-header">
                                    <div class="asset-name">网络交换机</div>
                                    <div class="asset-status asset-broken">故障</div>
                                </div>
                                <div class="asset-details">
                                    <div class="asset-detail">
                                        <span class="detail-label">资产编号</span>
                                        <span class="detail-value">AS003001</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">所在位置</span>
                                        <span class="detail-value">机房</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">购入日期</span>
                                        <span class="detail-value">2021-03-20</span>
                                    </div>
                                    <div class="asset-detail">
                                        <span class="detail-label">保修期</span>
                                        <span class="detail-value">已过期</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 能源监控 -->
                <div class="energy-monitor" id="energy-section" style="display: none;">
                    <h3 style="margin-bottom: 20px;">
                        <i class="fas fa-chart-line"></i> 能源使用监控
                    </h3>
                    <div class="energy-grid">
                        <div class="energy-item">
                            <div class="energy-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="energy-value">2,456</div>
                            <div class="energy-label">今日用电量 (kWh)</div>
                            <div class="energy-trend trend-down">
                                <i class="fas fa-arrow-down"></i> 比昨日减少 8.5%
                            </div>
                        </div>

                        <div class="energy-item">
                            <div class="energy-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="energy-value">1,234</div>
                            <div class="energy-label">今日用水量 (吨)</div>
                            <div class="energy-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 比昨日增加 3.2%
                            </div>
                        </div>

                        <div class="energy-item">
                            <div class="energy-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="energy-value">567</div>
                            <div class="energy-label">今日燃气量 (m³)</div>
                            <div class="energy-trend trend-down">
                                <i class="fas fa-arrow-down"></i> 比昨日减少 12.1%
                            </div>
                        </div>

                        <div class="energy-item">
                            <div class="energy-icon">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <div class="energy-value">22.5°C</div>
                            <div class="energy-label">平均室温</div>
                            <div class="energy-trend">
                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i> 舒适范围
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('校园服务 > 后勤服务系统');

        // 服务入口点击
        function openRepairService() {
            document.getElementById('repair-section').style.display = 'block';
            document.getElementById('repair-section').scrollIntoView({ behavior: 'smooth' });
        }

        function openAssetManagement() {
            document.getElementById('asset-section').style.display = 'block';
            document.getElementById('asset-section').scrollIntoView({ behavior: 'smooth' });
        }

        function openEnergyMonitor() {
            document.getElementById('energy-section').style.display = 'block';
            document.getElementById('energy-section').scrollIntoView({ behavior: 'smooth' });
        }

        function openEnvironmentControl() {
            SmartCampus.showMessage('环境控制系统正在开发中...', 'info');
        }

        function openCleaningService() {
            SmartCampus.showMessage('清洁服务管理正在开发中...', 'info');
        }

        function openSecurityService() {
            SmartCampus.showMessage('安保服务管理正在开发中...', 'info');
        }

        // 隐藏报修表单
        function hideRepairForm() {
            document.getElementById('repair-section').style.display = 'none';
        }

        // 提交报修
        document.querySelector('.repair-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('报修申请提交成功！工单号：WO202406004', 'success');
                hideRepairForm();
            }, 1500);
        });

        // 工单操作
        document.querySelectorAll('.repair-table .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`正在执行${action}操作...`, 'info');
            });
        });
    </script>
</body>
</html>
