<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .classroom-header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }

        .classroom-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .classroom-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(41,128,185,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .terminal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .terminal-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .terminal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .terminal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .terminal-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .terminal-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .status-busy {
            background: #fff3cd;
            color: #856404;
        }

        .terminal-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .terminal-controls {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .assignment-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .assignment-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .assignment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .assignment-info {
            flex: 1;
        }

        .assignment-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .assignment-meta {
            color: #666;
            font-size: 14px;
        }

        .assignment-actions {
            display: flex;
            gap: 8px;
        }

        .teaching-mode {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .mode-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .mode-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .mode-card:hover {
            background: #e9ecef;
            border-color: #3498db;
        }

        .mode-card.active {
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(41,128,185,0.1));
            border-color: #3498db;
        }

        .mode-icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #3498db;
        }

        .mode-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .mode-desc {
            color: #666;
            font-size: 12px;
        }

        .interaction-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .interaction-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .tool-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .tool-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52,152,219,0.3);
        }

        .tool-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .tool-label {
            font-size: 12px;
            font-weight: 500;
        }

        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .resource-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .resource-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .resource-type {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 15px;
        }

        .resource-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .resource-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .resource-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }

        .analytics-dashboard {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .analytics-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .student-progress {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .progress-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .student-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .progress-bar {
            width: 100px;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 0 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-score {
            font-weight: 500;
            color: #3498db;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <!-- 智慧课堂头部 -->
                <div class="classroom-header">
                    <h1 style="margin: 0 0 10px 0; font-size: 28px;">
                        <i class="fas fa-laptop"></i> 智慧课堂
                    </h1>
                    <p style="margin: 0; opacity: 0.9;">科技赋能教育，智慧点亮未来</p>
                </div>

                <!-- 课堂统计 -->
                <div class="classroom-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <div class="stat-number">45</div>
                        <div class="stat-label">智能终端</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">1,256</div>
                        <div class="stat-label">在线学生</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-number">89</div>
                        <div class="stat-label">活跃作业</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">92.5%</div>
                        <div class="stat-label">参与度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="stat-number">2.5TB</div>
                        <div class="stat-label">云端资源</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="classroom-tabs">
                    <button class="tab-btn active" data-tab="smart-terminals">
                        <i class="fas fa-desktop"></i> 智能终端
                    </button>
                    <button class="tab-btn" data-tab="online-assignments">
                        <i class="fas fa-clipboard-list"></i> 在线作业
                    </button>
                    <button class="tab-btn" data-tab="hybrid-teaching">
                        <i class="fas fa-chalkboard-teacher"></i> 混合教学
                    </button>
                    <button class="tab-btn" data-tab="interactive-tools">
                        <i class="fas fa-comments"></i> 课堂互动
                    </button>
                    <button class="tab-btn" data-tab="resource-sharing">
                        <i class="fas fa-share-alt"></i> 资源共享
                    </button>
                    <button class="tab-btn" data-tab="learning-analytics">
                        <i class="fas fa-chart-bar"></i> 学习分析
                    </button>
                </div>

                <!-- 智能终端 -->
                <div class="tab-content active" id="smart-terminals">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">智能教学终端管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="terminal-grid">
                                <div class="terminal-card">
                                    <div class="terminal-header">
                                        <div class="terminal-name">教室A101 - 主控终端</div>
                                        <div class="terminal-status status-online">在线</div>
                                    </div>
                                    <div class="terminal-info">
                                        <div class="info-item">
                                            <span class="info-label">设备型号</span>
                                            <span class="info-value">SmartBoard Pro</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">连接学生</span>
                                            <span class="info-value">45人</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">运行时间</span>
                                            <span class="info-value">2小时30分</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">系统版本</span>
                                            <span class="info-value">v2.1.5</span>
                                        </div>
                                    </div>
                                    <div class="terminal-controls">
                                        <button class="btn btn-primary" style="padding: 5px 15px; font-size: 12px;">控制</button>
                                        <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">投屏</button>
                                        <button class="btn btn-warning" style="padding: 5px 15px; font-size: 12px;">设置</button>
                                    </div>
                                </div>

                                <div class="terminal-card">
                                    <div class="terminal-header">
                                        <div class="terminal-name">教室B203 - 互动终端</div>
                                        <div class="terminal-status status-busy">使用中</div>
                                    </div>
                                    <div class="terminal-info">
                                        <div class="info-item">
                                            <span class="info-label">设备型号</span>
                                            <span class="info-value">TouchPanel X1</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">连接学生</span>
                                            <span class="info-value">38人</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">运行时间</span>
                                            <span class="info-value">1小时15分</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">系统版本</span>
                                            <span class="info-value">v2.0.8</span>
                                        </div>
                                    </div>
                                    <div class="terminal-controls">
                                        <button class="btn btn-primary" style="padding: 5px 15px; font-size: 12px;">控制</button>
                                        <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">投屏</button>
                                        <button class="btn btn-warning" style="padding: 5px 15px; font-size: 12px;">设置</button>
                                    </div>
                                </div>

                                <div class="terminal-card">
                                    <div class="terminal-header">
                                        <div class="terminal-name">实验室C305 - 实训终端</div>
                                        <div class="terminal-status status-offline">离线</div>
                                    </div>
                                    <div class="terminal-info">
                                        <div class="info-item">
                                            <span class="info-label">设备型号</span>
                                            <span class="info-value">LabStation 2.0</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">连接学生</span>
                                            <span class="info-value">0人</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">运行时间</span>
                                            <span class="info-value">0分钟</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">系统版本</span>
                                            <span class="info-value">v1.9.2</span>
                                        </div>
                                    </div>
                                    <div class="terminal-controls">
                                        <button class="btn btn-secondary" style="padding: 5px 15px; font-size: 12px;" disabled>控制</button>
                                        <button class="btn btn-danger" style="padding: 5px 15px; font-size: 12px;">重启</button>
                                        <button class="btn btn-warning" style="padding: 5px 15px; font-size: 12px;">诊断</button>
                                    </div>
                                </div>

                                <div class="terminal-card">
                                    <div class="terminal-header">
                                        <div class="terminal-name">多媒体教室D401</div>
                                        <div class="terminal-status status-online">在线</div>
                                    </div>
                                    <div class="terminal-info">
                                        <div class="info-item">
                                            <span class="info-label">设备型号</span>
                                            <span class="info-value">MediaHub Pro</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">连接学生</span>
                                            <span class="info-value">52人</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">运行时间</span>
                                            <span class="info-value">3小时45分</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">系统版本</span>
                                            <span class="info-value">v2.2.1</span>
                                        </div>
                                    </div>
                                    <div class="terminal-controls">
                                        <button class="btn btn-primary" style="padding: 5px 15px; font-size: 12px;">控制</button>
                                        <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">投屏</button>
                                        <button class="btn btn-warning" style="padding: 5px 15px; font-size: 12px;">设置</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('智慧课堂');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 智能终端控制
        document.querySelectorAll('.terminal-controls .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`${action}操作执行中...`, 'info');
            });
        });

        // 作业操作
        document.querySelectorAll('.assignment-actions .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();

                if (action === '查看') {
                    SmartCampus.showMessage('查看作业详情...', 'info');
                } else if (action === '批改') {
                    SmartCampus.showMessage('进入批改模式...', 'info');
                } else if (action === '统计') {
                    SmartCampus.showMessage('查看提交统计...', 'info');
                }
            });
        });

        // 教学资源操作
        document.querySelectorAll('.resource-actions .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();

                if (action === '下载') {
                    SmartCampus.showMessage('开始下载资源...', 'success');
                } else if (action === '分享') {
                    SmartCampus.showMessage('资源链接已复制', 'success');
                } else if (action === '编辑') {
                    SmartCampus.showMessage('进入编辑模式...', 'info');
                }
            });
        });

        // 互动工具点击
        document.querySelectorAll('.interaction-tool').forEach(tool => {
            tool.addEventListener('click', function() {
                const toolName = this.querySelector('h4').textContent;
                SmartCampus.showMessage(`启动${toolName}...`, 'info');
            });
        });

        // 学习分析卡片点击
        document.querySelectorAll('.analysis-card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('h4').textContent;
                SmartCampus.showMessage(`查看${title}详情...`, 'info');
            });
        });

        // 统计卡片点击
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', function() {
                const label = this.querySelector('.stat-label').textContent;
                SmartCampus.showMessage(`查看${label}详细数据...`, 'info');
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            const onlineCard = document.querySelector('.stat-card:nth-child(2) .stat-number');
            if (onlineCard) {
                const current = parseInt(onlineCard.textContent);
                const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
                const newValue = Math.max(0, current + change);
                onlineCard.textContent = newValue;
            }
        }, 15000);
    </script>
</body>
</html>
