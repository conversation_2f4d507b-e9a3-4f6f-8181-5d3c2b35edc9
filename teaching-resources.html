<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学资源管理 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .resource-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .schedule-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .schedule-header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 500;
            font-size: 14px;
        }

        .schedule-time {
            background: #f8f9fa;
            padding: 15px 10px;
            text-align: center;
            font-weight: 500;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .schedule-cell {
            background: white;
            padding: 10px;
            min-height: 80px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .schedule-cell:hover {
            background: #f8f9fa;
        }

        .schedule-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .schedule-item.conflict {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .classroom-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .classroom-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .classroom-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .classroom-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .classroom-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .classroom-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-available {
            background: #d4edda;
            color: #155724;
        }

        .status-occupied {
            background: #f8d7da;
            color: #721c24;
        }

        .status-maintenance {
            background: #fff3cd;
            color: #856404;
        }

        .equipment-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .equipment-tag {
            background: linear-gradient(45deg, #3498db, #2ecc71);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
        }

        .textbook-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .textbook-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .textbook-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .textbook-table tr:hover {
            background: #f8f9fa;
        }

        .stock-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .stock-high {
            background: #d4edda;
            color: #155724;
        }

        .stock-medium {
            background: #fff3cd;
            color: #856404;
        }

        .stock-low {
            background: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
        }

        .filter-select {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-book"></i> 教学资源管理
                </h1>

                <!-- 功能选项卡 -->
                <div class="resource-tabs">
                    <button class="tab-btn active" data-tab="schedule">
                        <i class="fas fa-calendar-alt"></i> 智能排课
                    </button>
                    <button class="tab-btn" data-tab="classroom">
                        <i class="fas fa-door-open"></i> 教室管理
                    </button>
                    <button class="tab-btn" data-tab="textbook">
                        <i class="fas fa-book-open"></i> 教材管理
                    </button>
                </div>

                <!-- 智能排课 -->
                <div class="tab-content active" id="schedule">
                    <div class="action-buttons">
                        <button class="btn btn-primary">
                            <i class="fas fa-magic"></i> 自动排课
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-plus"></i> 手动添加
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i> 冲突检测
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">本周课程表</h3>
                        </div>
                        <div class="card-body">
                            <div class="schedule-grid">
                                <div class="schedule-header">时间</div>
                                <div class="schedule-header">周一</div>
                                <div class="schedule-header">周二</div>
                                <div class="schedule-header">周三</div>
                                <div class="schedule-header">周四</div>
                                <div class="schedule-header">周五</div>
                                <div class="schedule-header">周六</div>
                                <div class="schedule-header">周日</div>

                                <div class="schedule-time">08:00<br>09:40</div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        高等数学<br>A101 张教授
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        英语听力<br>B203 李老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        计算机基础<br>C301 王老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        物理实验<br>D401 赵老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        化学原理<br>E501 陈老师
                                    </div>
                                </div>
                                <div class="schedule-cell"></div>
                                <div class="schedule-cell"></div>

                                <div class="schedule-time">10:00<br>11:40</div>
                                <div class="schedule-cell">
                                    <div class="schedule-item conflict">
                                        线性代数<br>A101 张教授
                                        <i class="fas fa-exclamation-triangle" style="float: right;"></i>
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        大学语文<br>B204 刘老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        数据结构<br>C302 王老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        有机化学<br>D402 赵老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        英语写作<br>E502 李老师
                                    </div>
                                </div>
                                <div class="schedule-cell"></div>
                                <div class="schedule-cell"></div>

                                <div class="schedule-time">14:00<br>15:40</div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        概率统计<br>A102 张教授
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        体育课<br>操场 孙老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        算法设计<br>C303 王老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        实验课<br>实验室 赵老师
                                    </div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">
                                        选修课<br>E503 陈老师
                                    </div>
                                </div>
                                <div class="schedule-cell"></div>
                                <div class="schedule-cell"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 教室管理 -->
                <div class="tab-content" id="classroom">
                    <div class="filter-bar">
                        <div class="filter-group">
                            <label>楼栋：</label>
                            <select class="filter-select">
                                <option>全部</option>
                                <option>教学楼A座</option>
                                <option>教学楼B座</option>
                                <option>实验楼C座</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>状态：</label>
                            <select class="filter-select">
                                <option>全部</option>
                                <option>可用</option>
                                <option>占用</option>
                                <option>维护</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>容量：</label>
                            <select class="filter-select">
                                <option>全部</option>
                                <option>小型(≤30人)</option>
                                <option>中型(31-60人)</option>
                                <option>大型(>60人)</option>
                            </select>
                        </div>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>

                    <div class="classroom-grid">
                        <div class="classroom-card">
                            <div class="classroom-header">
                                <div class="classroom-name">A101 多媒体教室</div>
                                <div class="classroom-status status-available">可用</div>
                            </div>
                            <div style="color: #666; margin-bottom: 10px;">
                                <i class="fas fa-users"></i> 容量: 60人 &nbsp;&nbsp;
                                <i class="fas fa-map-marker-alt"></i> 教学楼A座1层
                            </div>
                            <div class="equipment-list">
                                <span class="equipment-tag">投影仪</span>
                                <span class="equipment-tag">音响系统</span>
                                <span class="equipment-tag">空调</span>
                                <span class="equipment-tag">电子白板</span>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" style="margin-right: 10px;">预约</button>
                                <button class="btn btn-success">详情</button>
                            </div>
                        </div>

                        <div class="classroom-card">
                            <div class="classroom-header">
                                <div class="classroom-name">B203 语音教室</div>
                                <div class="classroom-status status-occupied">占用中</div>
                            </div>
                            <div style="color: #666; margin-bottom: 10px;">
                                <i class="fas fa-users"></i> 容量: 45人 &nbsp;&nbsp;
                                <i class="fas fa-map-marker-alt"></i> 教学楼B座2层
                            </div>
                            <div class="equipment-list">
                                <span class="equipment-tag">语音设备</span>
                                <span class="equipment-tag">耳机</span>
                                <span class="equipment-tag">录音系统</span>
                            </div>
                            <div style="margin-top: 15px; color: #e74c3c;">
                                <i class="fas fa-clock"></i> 占用至: 15:40 (英语听力课)
                            </div>
                        </div>

                        <div class="classroom-card">
                            <div class="classroom-header">
                                <div class="classroom-name">C301 计算机教室</div>
                                <div class="classroom-status status-maintenance">维护中</div>
                            </div>
                            <div style="color: #666; margin-bottom: 10px;">
                                <i class="fas fa-users"></i> 容量: 50人 &nbsp;&nbsp;
                                <i class="fas fa-map-marker-alt"></i> 实验楼C座3层
                            </div>
                            <div class="equipment-list">
                                <span class="equipment-tag">电脑50台</span>
                                <span class="equipment-tag">投影仪</span>
                                <span class="equipment-tag">网络</span>
                            </div>
                            <div style="margin-top: 15px; color: #f39c12;">
                                <i class="fas fa-wrench"></i> 预计维护完成: 明日上午
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 教材管理 -->
                <div class="tab-content" id="textbook">
                    <div class="action-buttons">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增教材
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-shopping-cart"></i> 批量订购
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-file-export"></i> 导出清单
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">教材库存管理</h3>
                        </div>
                        <div class="card-body">
                            <table class="textbook-table">
                                <thead>
                                    <tr>
                                        <th>教材名称</th>
                                        <th>作者</th>
                                        <th>出版社</th>
                                        <th>版本</th>
                                        <th>库存数量</th>
                                        <th>需求数量</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>高等数学(上册)</td>
                                        <td>同济大学数学系</td>
                                        <td>高等教育出版社</td>
                                        <td>第七版</td>
                                        <td>1,250</td>
                                        <td>1,200</td>
                                        <td><span class="stock-badge stock-high">库存充足</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>大学英语综合教程</td>
                                        <td>李霄翔</td>
                                        <td>上海外语教育出版社</td>
                                        <td>第三版</td>
                                        <td>680</td>
                                        <td>800</td>
                                        <td><span class="stock-badge stock-medium">需要补充</span></td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">订购</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>计算机网络</td>
                                        <td>谢希仁</td>
                                        <td>电子工业出版社</td>
                                        <td>第八版</td>
                                        <td>45</td>
                                        <td>300</td>
                                        <td><span class="stock-badge stock-low">库存不足</span></td>
                                        <td>
                                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">紧急订购</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>数据结构与算法</td>
                                        <td>严蔚敏</td>
                                        <td>清华大学出版社</td>
                                        <td>第二版</td>
                                        <td>890</td>
                                        <td>750</td>
                                        <td><span class="stock-badge stock-high">库存充足</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('教学管理 > 教学资源管理');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加当前活动状态
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 课程表单元格点击事件
        document.querySelectorAll('.schedule-cell').forEach(cell => {
            cell.addEventListener('click', function() {
                if (this.children.length === 0) {
                    SmartCampus.showMessage('点击空白时间段可添加新课程', 'info');
                } else {
                    SmartCampus.showMessage('点击已有课程可编辑或删除', 'info');
                }
            });
        });

        // 自动排课按钮
        document.querySelector('[data-tab="schedule"] .btn-primary').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('自动排课完成！检测到1个时间冲突，请手动调整。', 'warning');
            }, 2000);
        });
    </script>
</body>
</html>
