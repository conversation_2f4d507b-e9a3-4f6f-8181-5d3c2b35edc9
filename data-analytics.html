<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .analytics-header {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }

        .analytics-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .analytics-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .analytics-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #9b59b6, #8e44ad);
        }

        .analytics-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .analytics-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .analytics-label {
            color: #666;
            font-size: 14px;
        }

        .analytics-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #9b59b6;
            background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(142,68,173,0.1));
            border-bottom-color: #9b59b6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            margin-top: 15px;
        }

        .warning-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .warning-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #e74c3c;
        }

        .warning-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .warning-icon {
            color: #e74c3c;
            font-size: 20px;
        }

        .warning-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .warning-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .warning-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            color: #666;
            font-size: 14px;
        }

        .portrait-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .portrait-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }

        .portrait-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 15px;
        }

        .portrait-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .portrait-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            justify-content: center;
            margin-bottom: 15px;
        }

        .portrait-tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .portrait-score {
            font-size: 24px;
            font-weight: bold;
            color: #9b59b6;
        }

        .ai-insights {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .insight-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .insight-content {
            flex: 1;
        }

        .insight-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .insight-text {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .quality-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .metric-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
        }

        .operation-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .operation-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .operation-number {
            font-size: 20px;
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 5px;
        }

        .operation-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <!-- 数据分析头部 -->
                <div class="analytics-header">
                    <h1 style="margin: 0 0 10px 0; font-size: 28px;">
                        <i class="fas fa-chart-line"></i> 数据分析中心
                    </h1>
                    <p style="margin: 0; opacity: 0.9;">数据驱动决策，智能洞察未来</p>
                </div>

                <!-- 分析概览 -->
                <div class="analytics-dashboard">
                    <div class="analytics-card">
                        <div class="analytics-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="analytics-number">23</div>
                        <div class="analytics-label">学业预警</div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="analytics-number">1,256</div>
                        <div class="analytics-label">学生画像</div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="analytics-number">89</div>
                        <div class="analytics-label">AI分析</div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="analytics-number">4.8</div>
                        <div class="analytics-label">教学质量</div>
                    </div>
                    <div class="analytics-card">
                        <div class="analytics-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="analytics-number">95.2%</div>
                        <div class="analytics-label">运营效率</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="analytics-tabs">
                    <button class="tab-btn active" data-tab="academic-warning">
                        <i class="fas fa-exclamation-triangle"></i> 学业预警
                    </button>
                    <button class="tab-btn" data-tab="student-portrait">
                        <i class="fas fa-user-graduate"></i> 学生画像
                    </button>
                    <button class="tab-btn" data-tab="ai-analysis">
                        <i class="fas fa-brain"></i> AI智能分析
                    </button>
                    <button class="tab-btn" data-tab="teaching-quality">
                        <i class="fas fa-chart-bar"></i> 教学质量
                    </button>
                    <button class="tab-btn" data-tab="campus-operation">
                        <i class="fas fa-cogs"></i> 校园运营
                    </button>
                </div>

                <!-- 学业预警 -->
                <div class="tab-content active" id="academic-warning">
                    <div class="chart-grid">
                        <div class="chart-card">
                            <h4>预警趋势分析</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>预警趋势图表</span>
                            </div>
                        </div>
                        <div class="chart-card">
                            <h4>预警分类统计</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-pie" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>分类统计图表</span>
                            </div>
                        </div>
                    </div>

                    <div class="warning-grid">
                        <div class="warning-card">
                            <div class="warning-header">
                                <div class="warning-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="warning-title">学业预警学生</div>
                            </div>
                            <ul class="warning-list">
                                <li>张三 - 计算机学院 - 多门课程不及格</li>
                                <li>李四 - 数学学院 - 出勤率低于80%</li>
                                <li>王五 - 外语学院 - GPA低于2.0</li>
                                <li>赵六 - 物理学院 - 学分不足</li>
                                <li>陈七 - 化学学院 - 连续两学期成绩下降</li>
                            </ul>
                        </div>

                        <div class="warning-card">
                            <div class="warning-header">
                                <div class="warning-icon">
                                    <i class="fas fa-user-clock"></i>
                                </div>
                                <div class="warning-title">考勤异常</div>
                            </div>
                            <ul class="warning-list">
                                <li>孙八 - 本周缺课超过3次</li>
                                <li>周九 - 连续一周未签到</li>
                                <li>吴十 - 迟到次数超过10次</li>
                                <li>郑一 - 早退频繁</li>
                                <li>王二 - 请假时间过长</li>
                            </ul>
                        </div>

                        <div class="warning-card">
                            <div class="warning-header">
                                <div class="warning-icon">
                                    <i class="fas fa-heart-broken"></i>
                                </div>
                                <div class="warning-title">心理健康关注</div>
                            </div>
                            <ul class="warning-list">
                                <li>刘三 - 心理测评分数异常</li>
                                <li>陈四 - 社交活动参与度低</li>
                                <li>张五 - 情绪波动较大</li>
                                <li>李六 - 学习压力过大</li>
                                <li>王七 - 需要心理干预</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 学生画像 -->
                <div class="tab-content" id="student-portrait">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">学生个性化画像</h3>
                        </div>
                        <div class="card-body">
                            <div class="portrait-grid">
                                <div class="portrait-card">
                                    <div class="portrait-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="portrait-name">张三 - 优秀学生</div>
                                    <div class="portrait-tags">
                                        <span class="portrait-tag">学霸型</span>
                                        <span class="portrait-tag">活跃</span>
                                        <span class="portrait-tag">领导力</span>
                                    </div>
                                    <div class="portrait-score">95分</div>
                                </div>

                                <div class="portrait-card">
                                    <div class="portrait-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="portrait-name">李四 - 潜力学生</div>
                                    <div class="portrait-tags">
                                        <span class="portrait-tag">创新型</span>
                                        <span class="portrait-tag">专注</span>
                                        <span class="portrait-tag">技术</span>
                                    </div>
                                    <div class="portrait-score">88分</div>
                                </div>

                                <div class="portrait-card">
                                    <div class="portrait-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="portrait-name">王五 - 关注学生</div>
                                    <div class="portrait-tags">
                                        <span class="portrait-tag">内向型</span>
                                        <span class="portrait-tag">需帮助</span>
                                        <span class="portrait-tag">基础薄弱</span>
                                    </div>
                                    <div class="portrait-score">72分</div>
                                </div>

                                <div class="portrait-card">
                                    <div class="portrait-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="portrait-name">赵六 - 全面发展</div>
                                    <div class="portrait-tags">
                                        <span class="portrait-tag">均衡型</span>
                                        <span class="portrait-tag">社交</span>
                                        <span class="portrait-tag">稳定</span>
                                    </div>
                                    <div class="portrait-score">85分</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI智能分析 -->
                <div class="tab-content" id="ai-analysis">
                    <div class="ai-insights">
                        <h4 style="margin-bottom: 20px;">AI智能洞察</h4>
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">学习模式分析</div>
                                <div class="insight-text">
                                    通过分析学生的学习行为数据，发现70%的学生更适合视觉化学习方式，建议增加多媒体教学内容。
                                </div>
                            </div>
                        </div>

                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">成绩预测模型</div>
                                <div class="insight-text">
                                    基于历史数据建立的成绩预测模型显示，当前有15名学生存在期末考试不及格风险，建议提前干预。
                                </div>
                            </div>
                        </div>

                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">群体行为分析</div>
                                <div class="insight-text">
                                    分析发现图书馆使用高峰期为晚上7-9点，建议在此时段增加座位和延长开放时间。
                                </div>
                            </div>
                        </div>

                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">个性化推荐</div>
                                <div class="insight-text">
                                    AI系统为每位学生生成个性化学习路径，平均提升学习效率25%，建议全面推广应用。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 教学质量 -->
                <div class="tab-content" id="teaching-quality">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">教学质量分析</h3>
                        </div>
                        <div class="card-body">
                            <div class="quality-metrics">
                                <div class="metric-item">
                                    <div class="metric-value">4.8</div>
                                    <div class="metric-label">平均教学评分</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">92.5%</div>
                                    <div class="metric-label">学生满意度</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">89.3%</div>
                                    <div class="metric-label">课程完成率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">95.1%</div>
                                    <div class="metric-label">教师出勤率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">87.6%</div>
                                    <div class="metric-label">作业提交率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">4.6</div>
                                    <div class="metric-label">课程难度评价</div>
                                </div>
                            </div>

                            <div class="chart-placeholder" style="margin-top: 20px;">
                                <i class="fas fa-chart-bar" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>教学质量趋势图表</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 校园运营 -->
                <div class="tab-content" id="campus-operation">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">校园运营分析</h3>
                        </div>
                        <div class="card-body">
                            <div class="operation-stats">
                                <div class="operation-item">
                                    <div class="operation-number">1,256</div>
                                    <div class="operation-label">日均访客</div>
                                </div>
                                <div class="operation-item">
                                    <div class="operation-number">89.5%</div>
                                    <div class="operation-label">设备利用率</div>
                                </div>
                                <div class="operation-item">
                                    <div class="operation-number">2.3万</div>
                                    <div class="operation-label">日均用电量</div>
                                </div>
                                <div class="operation-item">
                                    <div class="operation-number">95.2%</div>
                                    <div class="operation-label">服务满意度</div>
                                </div>
                                <div class="operation-item">
                                    <div class="operation-number">12</div>
                                    <div class="operation-label">待处理工单</div>
                                </div>
                                <div class="operation-item">
                                    <div class="operation-number">99.8%</div>
                                    <div class="operation-label">系统可用性</div>
                                </div>
                            </div>

                            <div class="chart-placeholder" style="margin-top: 20px;">
                                <i class="fas fa-chart-area" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>校园运营数据图表</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('数据分析');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 学生画像点击
        document.querySelectorAll('.portrait-card').forEach(card => {
            card.addEventListener('click', function() {
                SmartCampus.showMessage('查看详细学生画像...', 'info');
            });
        });

        // 预警项点击
        document.querySelectorAll('.warning-item').forEach(item => {
            item.addEventListener('click', function() {
                SmartCampus.showMessage('查看预警详情...', 'info');
            });
        });

        // AI洞察点击
        document.querySelectorAll('.insight-item').forEach(item => {
            item.addEventListener('click', function() {
                SmartCampus.showMessage('查看AI分析详情...', 'info');
            });
        });
    </script>
</body>
</html>
