<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息发布门户 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .portal-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }

        .portal-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .portal-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #e74c3c;
            background: linear-gradient(135deg, rgba(231,76,60,0.1), rgba(192,57,43,0.1));
            border-bottom-color: #e74c3c;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .content-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .editor-toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            flex-wrap: wrap;
        }

        .editor-btn {
            background: none;
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #495057;
        }

        .editor-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .content-editor {
            min-height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: inherit;
            resize: vertical;
        }

        .news-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .news-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .news-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .news-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }

        .news-content {
            padding: 20px;
        }

        .news-category {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 10px;
        }

        .news-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .news-excerpt {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }

        .notification-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .notification-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .notification-text {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .notification-time {
            color: #999;
            font-size: 12px;
        }

        .channel-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .channel-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .channel-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .channel-icon {
            font-size: 48px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .channel-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .channel-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .channel-stats {
            display: flex;
            justify-content: space-around;
            font-size: 12px;
            color: #999;
        }

        .activity-timeline {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .timeline-item {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 40px;
            bottom: -20px;
            width: 2px;
            background: #f0f0f0;
        }

        .timeline-item:last-child::before {
            display: none;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
            z-index: 1;
            position: relative;
        }

        .timeline-content {
            flex: 1;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .timeline-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .timeline-text {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .timeline-time {
            color: #999;
            font-size: 12px;
        }

        .analytics-summary {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <!-- 信息门户头部 -->
                <div class="portal-header">
                    <h1 style="margin: 0 0 10px 0; font-size: 28px;">
                        <i class="fas fa-bullhorn"></i> 信息发布门户
                    </h1>
                    <p style="margin: 0; opacity: 0.9;">统一信息发布平台，连接校园每一个角落</p>
                </div>

                <!-- 门户统计 -->
                <div class="portal-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <div class="stat-number">1,256</div>
                        <div class="stat-label">发布内容</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-number">45,678</div>
                        <div class="stat-label">总浏览量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">8,934</div>
                        <div class="stat-label">活跃用户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-share"></i>
                        </div>
                        <div class="stat-number">2,345</div>
                        <div class="stat-label">分享次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">今日通知</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="portal-tabs">
                    <button class="tab-btn active" data-tab="content-management">
                        <i class="fas fa-edit"></i> 内容管理
                    </button>
                    <button class="tab-btn" data-tab="news-announcements">
                        <i class="fas fa-newspaper"></i> 新闻公告
                    </button>
                    <button class="tab-btn" data-tab="notifications">
                        <i class="fas fa-bell"></i> 通知推送
                    </button>
                    <button class="tab-btn" data-tab="multi-channel">
                        <i class="fas fa-broadcast-tower"></i> 多渠道发布
                    </button>
                    <button class="tab-btn" data-tab="activity-info">
                        <i class="fas fa-calendar-alt"></i> 活动信息
                    </button>
                    <button class="tab-btn" data-tab="info-analytics">
                        <i class="fas fa-chart-pie"></i> 信息统计
                    </button>
                </div>

                <!-- 内容管理 -->
                <div class="tab-content active" id="content-management">
                    <div class="content-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-plus-circle"></i> 发布新内容
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">内容标题</label>
                                <input type="text" class="form-control" placeholder="请输入内容标题">
                            </div>
                            <div class="form-group">
                                <label class="form-label">内容分类</label>
                                <select class="form-control">
                                    <option>学校新闻</option>
                                    <option>通知公告</option>
                                    <option>学术活动</option>
                                    <option>招生信息</option>
                                    <option>就业指导</option>
                                    <option>校园生活</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">发布范围</label>
                                <select class="form-control">
                                    <option>全校师生</option>
                                    <option>仅教师</option>
                                    <option>仅学生</option>
                                    <option>指定学院</option>
                                    <option>指定年级</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">发布时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">内容摘要</label>
                            <textarea class="form-control" rows="3" placeholder="请输入内容摘要..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">正文内容</label>
                            <div class="editor-toolbar">
                                <button class="editor-btn"><i class="fas fa-bold"></i></button>
                                <button class="editor-btn"><i class="fas fa-italic"></i></button>
                                <button class="editor-btn"><i class="fas fa-underline"></i></button>
                                <button class="editor-btn"><i class="fas fa-list-ul"></i></button>
                                <button class="editor-btn"><i class="fas fa-list-ol"></i></button>
                                <button class="editor-btn"><i class="fas fa-link"></i></button>
                                <button class="editor-btn"><i class="fas fa-image"></i></button>
                                <button class="editor-btn"><i class="fas fa-table"></i></button>
                            </div>
                            <textarea class="content-editor" placeholder="请输入正文内容..."></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">附件上传</label>
                                <input type="file" class="form-control" multiple>
                            </div>
                            <div class="form-group">
                                <label class="form-label">标签</label>
                                <input type="text" class="form-control" placeholder="请输入标签，用逗号分隔">
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-paper-plane"></i> 立即发布
                            </button>
                            <button class="btn btn-secondary" style="padding: 12px 40px; margin-left: 10px;">
                                <i class="fas fa-save"></i> 保存草稿
                            </button>
                            <button class="btn btn-warning" style="padding: 12px 40px; margin-left: 10px;">
                                <i class="fas fa-eye"></i> 预览
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 新闻公告 -->
                <div class="tab-content" id="news-announcements">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最新新闻公告</h3>
                        </div>
                        <div class="card-body">
                            <div class="news-grid">
                                <div class="news-card">
                                    <div class="news-image">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <div class="news-content">
                                        <div class="news-category">学校新闻</div>
                                        <div class="news-title">我校在全国大学生创新创业大赛中获得佳绩</div>
                                        <div class="news-excerpt">
                                            在刚刚结束的第九届全国大学生创新创业大赛中，我校学生团队表现优异，共获得金奖2项、银奖5项、铜奖8项的优异成绩...
                                        </div>
                                        <div class="news-meta">
                                            <span>发布者：宣传部</span>
                                            <span>2024-06-25</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="news-card">
                                    <div class="news-image">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="news-content">
                                        <div class="news-category">通知公告</div>
                                        <div class="news-title">关于2024年暑期放假安排的通知</div>
                                        <div class="news-excerpt">
                                            根据学校教学安排和国家法定节假日规定，现将2024年暑期放假安排通知如下：学生放假时间为7月15日至9月1日...
                                        </div>
                                        <div class="news-meta">
                                            <span>发布者：教务处</span>
                                            <span>2024-06-24</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="news-card">
                                    <div class="news-image">
                                        <i class="fas fa-microscope"></i>
                                    </div>
                                    <div class="news-content">
                                        <div class="news-category">学术活动</div>
                                        <div class="news-title">人工智能前沿技术学术报告会</div>
                                        <div class="news-excerpt">
                                            特邀清华大学人工智能研究院院长张教授来我校作"深度学习在自然语言处理中的最新进展"学术报告...
                                        </div>
                                        <div class="news-meta">
                                            <span>发布者：科研处</span>
                                            <span>2024-06-23</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="news-card">
                                    <div class="news-image">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="news-content">
                                        <div class="news-category">招生信息</div>
                                        <div class="news-title">2024年研究生招生复试工作安排</div>
                                        <div class="news-excerpt">
                                            2024年硕士研究生招生复试工作即将开始，现将相关安排通知如下：复试时间为3月20日至3月25日...
                                        </div>
                                        <div class="news-meta">
                                            <span>发布者：研究生院</span>
                                            <span>2024-06-22</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('信息发布');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 发布内容表单提交
        document.getElementById('publishForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const title = document.getElementById('contentTitle').value;
            const category = document.getElementById('contentCategory').value;
            const audience = document.getElementById('publishAudience').value;
            const content = document.getElementById('contentBody').value;

            if (!title || !content) {
                SmartCampus.showMessage('请填写标题和内容', 'error');
                return;
            }

            SmartCampus.showMessage('内容发布成功！', 'success');

            // 重置表单
            this.reset();

            // 模拟添加到内容列表
            setTimeout(() => {
                location.reload();
            }, 1500);
        });

        // 内容管理操作
        document.querySelectorAll('.content-actions .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();

                if (action === '编辑') {
                    SmartCampus.showMessage('进入编辑模式...', 'info');
                } else if (action === '删除') {
                    if (confirm('确定要删除这条内容吗？')) {
                        SmartCampus.showMessage('内容已删除', 'success');
                        this.closest('.content-item').remove();
                    }
                } else if (action === '置顶') {
                    SmartCampus.showMessage('内容已置顶', 'success');
                } else if (action === '推送') {
                    SmartCampus.showMessage('内容推送成功！', 'success');
                }
            });
        });

        // 内容项点击查看详情
        document.querySelectorAll('.content-item').forEach(item => {
            item.addEventListener('click', function() {
                SmartCampus.showMessage('查看内容详情...', 'info');
            });
        });

        // 通知推送操作
        document.querySelectorAll('.notification-actions .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();

                if (action === '立即推送') {
                    SmartCampus.showMessage('通知推送成功！', 'success');
                } else if (action === '编辑') {
                    SmartCampus.showMessage('进入编辑模式...', 'info');
                } else if (action === '删除') {
                    if (confirm('确定要删除这条通知吗？')) {
                        SmartCampus.showMessage('通知已删除', 'success');
                        this.closest('.notification-item').remove();
                    }
                }
            });
        });

        // 活动信息操作
        document.querySelectorAll('.activity-actions .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();

                if (action === '报名') {
                    SmartCampus.showMessage('报名成功！', 'success');
                } else if (action === '详情') {
                    SmartCampus.showMessage('查看活动详情...', 'info');
                } else if (action === '分享') {
                    SmartCampus.showMessage('活动链接已复制到剪贴板', 'success');
                }
            });
        });

        // 统计卡片点击
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', function() {
                const label = this.querySelector('.stat-label').textContent;
                SmartCampus.showMessage(`查看${label}详细数据...`, 'info');
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            const viewCard = document.querySelector('.stat-card:nth-child(2) .stat-number');
            if (viewCard) {
                const currentViews = parseInt(viewCard.textContent.replace(/,/g, ''));
                const newViews = currentViews + Math.floor(Math.random() * 10) + 1;
                viewCard.textContent = newViews.toLocaleString();
            }
        }, 10000);
    </script>
</body>
</html>
