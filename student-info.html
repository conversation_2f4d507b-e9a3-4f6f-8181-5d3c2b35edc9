<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生信息中心 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .student-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .student-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            border: 4px solid rgba(255,255,255,0.3);
        }

        .student-basic-info {
            flex: 1;
        }

        .student-name {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .student-id {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .student-tags {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .student-tag {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .info-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .info-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #555;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .grade-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .grade-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .grade-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .grade-table tr:hover {
            background: #f8f9fa;
        }

        .grade-score {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .grade-excellent {
            background: #d4edda;
            color: #155724;
        }

        .grade-good {
            background: #cce5ff;
            color: #004085;
        }

        .grade-average {
            background: #fff3cd;
            color: #856404;
        }

        .grade-poor {
            background: #f8d7da;
            color: #721c24;
        }

        .activity-timeline {
            position: relative;
            padding-left: 30px;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3498db, #2ecc71);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #3498db;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #3498db;
        }

        .timeline-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .timeline-title {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .timeline-desc {
            font-size: 14px;
            color: #666;
        }

        .award-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .award-icon {
            font-size: 32px;
            opacity: 0.9;
        }

        .award-info {
            flex: 1;
        }

        .award-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .award-date {
            font-size: 12px;
            opacity: 0.8;
        }

        .family-member {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 3px;
        }

        .member-relation {
            font-size: 12px;
            color: #666;
        }

        .contact-info {
            font-size: 14px;
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-id-card"></i> 学生信息中心
                </h1>

                <!-- 学生基本信息头部 -->
                <div class="student-header">
                    <div class="student-avatar">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="student-basic-info">
                        <div class="student-name">张三</div>
                        <div class="student-id">学号：2024001001</div>
                        <div class="student-tags">
                            <span class="student-tag">计算机科学与技术</span>
                            <span class="student-tag">2024级</span>
                            <span class="student-tag">计科1班</span>
                            <span class="student-tag">班长</span>
                            <span class="student-tag">三好学生</span>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-primary" style="margin-bottom: 10px;">
                            <i class="fas fa-edit"></i> 编辑信息
                        </button>
                        <br>
                        <button class="btn btn-success">
                            <i class="fas fa-print"></i> 打印档案
                        </button>
                    </div>
                </div>

                <!-- 信息选项卡 -->
                <div class="info-tabs">
                    <button class="tab-btn active" data-tab="basic-info">
                        <i class="fas fa-user"></i> 基本信息
                    </button>
                    <button class="tab-btn" data-tab="academic-info">
                        <i class="fas fa-graduation-cap"></i> 学业信息
                    </button>
                    <button class="tab-btn" data-tab="family-info">
                        <i class="fas fa-home"></i> 家庭信息
                    </button>
                    <button class="tab-btn" data-tab="activity-record">
                        <i class="fas fa-trophy"></i> 活动记录
                    </button>
                </div>

                <!-- 基本信息 -->
                <div class="tab-content active" id="basic-info">
                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-id-card"></i> 个人信息
                            </div>
                            <div class="info-item">
                                <span class="info-label">姓名</span>
                                <span class="info-value">张三</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">性别</span>
                                <span class="info-value">男</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">出生日期</span>
                                <span class="info-value">2006年3月15日</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">民族</span>
                                <span class="info-value">汉族</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">政治面貌</span>
                                <span class="info-value">共青团员</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">身份证号</span>
                                <span class="info-value">110101200603150001</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-map-marker-alt"></i> 联系信息
                            </div>
                            <div class="info-item">
                                <span class="info-label">手机号码</span>
                                <span class="info-value">138****1234</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">电子邮箱</span>
                                <span class="info-value"><EMAIL></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">QQ号码</span>
                                <span class="info-value">123456789</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">微信号</span>
                                <span class="info-value">zhangsan2024</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">家庭住址</span>
                                <span class="info-value">北京市朝阳区xxx街道xxx号</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">宿舍地址</span>
                                <span class="info-value">学生公寓1号楼201室</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-school"></i> 入学信息
                            </div>
                            <div class="info-item">
                                <span class="info-label">入学时间</span>
                                <span class="info-value">2024年9月1日</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">学制</span>
                                <span class="info-value">四年制本科</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">学习形式</span>
                                <span class="info-value">全日制</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">录取批次</span>
                                <span class="info-value">本科一批</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">高考分数</span>
                                <span class="info-value">628分</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">生源地</span>
                                <span class="info-value">北京市</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-heart"></i> 健康信息
                            </div>
                            <div class="info-item">
                                <span class="info-label">身高</span>
                                <span class="info-value">175cm</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">体重</span>
                                <span class="info-value">65kg</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">血型</span>
                                <span class="info-value">A型</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">视力状况</span>
                                <span class="info-value">正常</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">过敏史</span>
                                <span class="info-value">无</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">特殊疾病</span>
                                <span class="info-value">无</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 学业信息 -->
                <div class="tab-content" id="academic-info">
                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-chart-line"></i> 学业概况
                            </div>
                            <div class="info-item">
                                <span class="info-label">当前学期</span>
                                <span class="info-value">2024春季学期</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">已修学分</span>
                                <span class="info-value">45/160</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">平均绩点</span>
                                <span class="info-value">3.8/4.0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">专业排名</span>
                                <span class="info-value">5/120</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">学业状态</span>
                                <span class="info-value" style="color: #2ecc71;">正常</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">预计毕业</span>
                                <span class="info-value">2028年6月</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-certificate"></i> 证书获得
                            </div>
                            <div class="info-item">
                                <span class="info-label">英语四级</span>
                                <span class="info-value" style="color: #2ecc71;">已通过 (580分)</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">英语六级</span>
                                <span class="info-value" style="color: #f39c12;">未参加</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">计算机二级</span>
                                <span class="info-value" style="color: #2ecc71;">已通过</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">普通话</span>
                                <span class="info-value" style="color: #2ecc71;">二级甲等</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">专业证书</span>
                                <span class="info-value" style="color: #f39c12;">准备中</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar"></i> 成绩记录
                            </h3>
                        </div>
                        <div class="card-body">
                            <table class="grade-table">
                                <thead>
                                    <tr>
                                        <th>课程名称</th>
                                        <th>学分</th>
                                        <th>成绩</th>
                                        <th>绩点</th>
                                        <th>学期</th>
                                        <th>考试性质</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>高等数学A(上)</td>
                                        <td>5</td>
                                        <td><span class="grade-score grade-excellent">92</span></td>
                                        <td>4.0</td>
                                        <td>2024秋</td>
                                        <td>必修</td>
                                    </tr>
                                    <tr>
                                        <td>大学英语(1)</td>
                                        <td>4</td>
                                        <td><span class="grade-score grade-good">85</span></td>
                                        <td>3.5</td>
                                        <td>2024秋</td>
                                        <td>必修</td>
                                    </tr>
                                    <tr>
                                        <td>计算机导论</td>
                                        <td>3</td>
                                        <td><span class="grade-score grade-excellent">95</span></td>
                                        <td>4.0</td>
                                        <td>2024秋</td>
                                        <td>专业必修</td>
                                    </tr>
                                    <tr>
                                        <td>思想道德与法治</td>
                                        <td>3</td>
                                        <td><span class="grade-score grade-good">88</span></td>
                                        <td>3.8</td>
                                        <td>2024秋</td>
                                        <td>必修</td>
                                    </tr>
                                    <tr>
                                        <td>体育(1)</td>
                                        <td>1</td>
                                        <td><span class="grade-score grade-excellent">90</span></td>
                                        <td>4.0</td>
                                        <td>2024秋</td>
                                        <td>必修</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 家庭信息 -->
                <div class="tab-content" id="family-info">
                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-home"></i> 家庭概况
                            </div>
                            <div class="info-item">
                                <span class="info-label">家庭性质</span>
                                <span class="info-value">城镇家庭</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">经济状况</span>
                                <span class="info-value">中等</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">家庭人口</span>
                                <span class="info-value">4人</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">是否单亲</span>
                                <span class="info-value">否</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">困难生认定</span>
                                <span class="info-value">否</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-users"></i> 家庭成员
                            </div>
                            <div class="family-member">
                                <div class="member-avatar">
                                    <i class="fas fa-male"></i>
                                </div>
                                <div class="member-info">
                                    <div class="member-name">张建国</div>
                                    <div class="member-relation">父亲 | 工程师</div>
                                </div>
                                <div class="contact-info">138****5678</div>
                            </div>
                            <div class="family-member">
                                <div class="member-avatar">
                                    <i class="fas fa-female"></i>
                                </div>
                                <div class="member-info">
                                    <div class="member-name">李美华</div>
                                    <div class="member-relation">母亲 | 教师</div>
                                </div>
                                <div class="contact-info">139****9876</div>
                            </div>
                            <div class="family-member">
                                <div class="member-avatar">
                                    <i class="fas fa-child"></i>
                                </div>
                                <div class="member-info">
                                    <div class="member-name">张小明</div>
                                    <div class="member-relation">弟弟 | 高中生</div>
                                </div>
                                <div class="contact-info">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 活动记录 -->
                <div class="tab-content" id="activity-record">
                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-trophy"></i> 获奖记录
                            </div>
                            <div class="award-card">
                                <div class="award-icon">
                                    <i class="fas fa-medal"></i>
                                </div>
                                <div class="award-info">
                                    <div class="award-title">三好学生</div>
                                    <div class="award-date">2024年春季学期</div>
                                </div>
                            </div>
                            <div class="award-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                <div class="award-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="award-info">
                                    <div class="award-title">优秀班干部</div>
                                    <div class="award-date">2024年春季学期</div>
                                </div>
                            </div>
                            <div class="award-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                                <div class="award-icon">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div class="award-info">
                                    <div class="award-title">程序设计竞赛三等奖</div>
                                    <div class="award-date">2024年4月</div>
                                </div>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="fas fa-history"></i> 活动时间线
                            </div>
                            <div class="activity-timeline">
                                <div class="timeline-item">
                                    <div class="timeline-date">2024年6月15日</div>
                                    <div class="timeline-title">期末考试</div>
                                    <div class="timeline-desc">参加高等数学期末考试，成绩优异</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2024年5月20日</div>
                                    <div class="timeline-title">社团活动</div>
                                    <div class="timeline-desc">参加计算机协会技术分享会，担任主讲人</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2024年4月18日</div>
                                    <div class="timeline-title">竞赛获奖</div>
                                    <div class="timeline-desc">在校级程序设计竞赛中获得三等奖</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2024年3月15日</div>
                                    <div class="timeline-title">当选班长</div>
                                    <div class="timeline-desc">在班级选举中当选为班长，开始服务同学</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2024年2月28日</div>
                                    <div class="timeline-title">志愿服务</div>
                                    <div class="timeline-desc">参加校园清洁志愿服务活动，服务时长8小时</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2024年1月10日</div>
                                    <div class="timeline-title">期末总结</div>
                                    <div class="timeline-desc">第一学期结束，各科成绩优异，获得三好学生称号</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('学生管理 > 学生信息中心');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 编辑信息按钮
        document.querySelector('.btn-primary').addEventListener('click', function() {
            SmartCampus.showMessage('进入编辑模式，可修改学生基本信息', 'info');
        });

        // 打印档案按钮
        document.querySelector('.btn-success').addEventListener('click', function() {
            SmartCampus.showMessage('正在生成学生档案PDF文件...', 'info');
            setTimeout(() => {
                SmartCampus.showMessage('档案生成完成，开始下载', 'success');
            }, 2000);
        });
    </script>
</body>
</html>