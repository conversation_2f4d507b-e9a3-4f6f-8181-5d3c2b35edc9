<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .settings-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }

        .settings-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #34495e;
            background: linear-gradient(135deg, rgba(52,73,94,0.1), rgba(44,62,80,0.1));
            border-bottom-color: #34495e;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .settings-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .permission-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .permission-card:hover {
            border-color: #34495e;
        }

        .permission-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .permission-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .permission-toggle {
            position: relative;
            width: 40px;
            height: 20px;
            background: #ccc;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .permission-toggle.active {
            background: #34495e;
        }

        .permission-toggle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .permission-toggle.active::before {
            transform: translateX(20px);
        }

        .permission-desc {
            color: #666;
            font-size: 12px;
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .backup-info {
            flex: 1;
        }

        .backup-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .backup-meta {
            color: #666;
            font-size: 14px;
        }

        .backup-actions {
            display: flex;
            gap: 8px;
        }

        .log-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .log-table th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .log-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .log-table tr:hover {
            background: #f8f9fa;
        }

        .log-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .level-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .level-warning {
            background: #fff3cd;
            color: #856404;
        }

        .level-error {
            background: #f8d7da;
            color: #721c24;
        }

        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .monitor-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }

        .monitor-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .monitor-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .monitor-label {
            color: #666;
            font-size: 14px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background: #2ecc71;
        }

        .status-offline {
            background: #e74c3c;
        }

        .status-warning {
            background: #f39c12;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <!-- 系统设置头部 -->
                <div class="settings-header">
                    <h1 style="margin: 0 0 10px 0; font-size: 28px;">
                        <i class="fas fa-cogs"></i> 系统设置
                    </h1>
                    <p style="margin: 0; opacity: 0.9;">系统配置与管理中心</p>
                </div>

                <!-- 功能选项卡 -->
                <div class="settings-tabs">
                    <button class="tab-btn active" data-tab="user-permissions">
                        <i class="fas fa-users-cog"></i> 用户权限
                    </button>
                    <button class="tab-btn" data-tab="system-config">
                        <i class="fas fa-sliders-h"></i> 系统配置
                    </button>
                    <button class="tab-btn" data-tab="data-backup">
                        <i class="fas fa-database"></i> 数据备份
                    </button>
                    <button class="tab-btn" data-tab="security-settings">
                        <i class="fas fa-shield-alt"></i> 安全设置
                    </button>
                    <button class="tab-btn" data-tab="system-monitor">
                        <i class="fas fa-desktop"></i> 系统监控
                    </button>
                    <button class="tab-btn" data-tab="log-management">
                        <i class="fas fa-file-alt"></i> 日志管理
                    </button>
                </div>

                <!-- 用户权限 -->
                <div class="tab-content active" id="user-permissions">
                    <div class="settings-form">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-user-shield"></i>
                                角色权限管理
                            </div>
                            <div class="permission-grid">
                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-name">超级管理员</div>
                                        <div class="permission-toggle active"></div>
                                    </div>
                                    <div class="permission-desc">拥有系统所有权限</div>
                                </div>

                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-name">教务管理员</div>
                                        <div class="permission-toggle active"></div>
                                    </div>
                                    <div class="permission-desc">管理教学相关功能</div>
                                </div>

                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-name">学生管理员</div>
                                        <div class="permission-toggle active"></div>
                                    </div>
                                    <div class="permission-desc">管理学生信息和服务</div>
                                </div>

                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-name">教师用户</div>
                                        <div class="permission-toggle active"></div>
                                    </div>
                                    <div class="permission-desc">教师基本功能权限</div>
                                </div>

                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-name">学生用户</div>
                                        <div class="permission-toggle active"></div>
                                    </div>
                                    <div class="permission-desc">学生基本功能权限</div>
                                </div>

                                <div class="permission-card">
                                    <div class="permission-header">
                                        <div class="permission-name">访客用户</div>
                                        <div class="permission-toggle"></div>
                                    </div>
                                    <div class="permission-desc">临时访问权限</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-key"></i>
                                功能权限配置
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">用户注册</label>
                                    <select class="form-control">
                                        <option>开放注册</option>
                                        <option>邀请注册</option>
                                        <option>管理员审核</option>
                                        <option>关闭注册</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">密码策略</label>
                                    <select class="form-control">
                                        <option>强密码（推荐）</option>
                                        <option>中等强度</option>
                                        <option>简单密码</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">会话超时</label>
                                    <select class="form-control">
                                        <option>30分钟</option>
                                        <option>1小时</option>
                                        <option>2小时</option>
                                        <option>4小时</option>
                                        <option>8小时</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">多设备登录</label>
                                    <select class="form-control">
                                        <option>允许</option>
                                        <option>限制3台设备</option>
                                        <option>限制1台设备</option>
                                        <option>禁止</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 系统配置 -->
                <div class="tab-content" id="system-config">
                    <div class="settings-form">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-server"></i>
                                系统基础配置
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">系统名称</label>
                                    <input type="text" class="form-control" value="智慧校园管理系统">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">系统版本</label>
                                    <input type="text" class="form-control" value="v2.1.0" readonly>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">维护模式</label>
                                    <select class="form-control">
                                        <option>关闭</option>
                                        <option>开启</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">调试模式</label>
                                    <select class="form-control">
                                        <option>关闭</option>
                                        <option>开启</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-envelope"></i>
                                邮件配置
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">SMTP服务器</label>
                                    <input type="text" class="form-control" placeholder="smtp.example.com">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">端口</label>
                                    <input type="number" class="form-control" value="587">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-control" placeholder="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">密码</label>
                                    <input type="password" class="form-control" placeholder="••••••••">
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-save"></i> 保存配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据备份 -->
                <div class="tab-content" id="data-backup">
                    <div class="settings-form">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-database"></i>
                                自动备份设置
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">备份频率</label>
                                    <select class="form-control">
                                        <option>每日备份</option>
                                        <option>每周备份</option>
                                        <option>每月备份</option>
                                        <option>手动备份</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">备份时间</label>
                                    <input type="time" class="form-control" value="02:00">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">保留天数</label>
                                    <input type="number" class="form-control" value="30">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">备份位置</label>
                                    <select class="form-control">
                                        <option>本地存储</option>
                                        <option>云端存储</option>
                                        <option>远程服务器</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-history"></i>
                                备份历史
                            </div>
                            <div class="backup-item">
                                <div class="backup-info">
                                    <div class="backup-name">系统完整备份_20240625</div>
                                    <div class="backup-meta">大小: 2.3GB | 类型: 完整备份 | 状态: 成功</div>
                                </div>
                                <div class="backup-actions">
                                    <button class="btn btn-primary btn-sm">恢复</button>
                                    <button class="btn btn-success btn-sm">下载</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                </div>
                            </div>
                            <div class="backup-item">
                                <div class="backup-info">
                                    <div class="backup-name">增量备份_20240624</div>
                                    <div class="backup-meta">大小: 156MB | 类型: 增量备份 | 状态: 成功</div>
                                </div>
                                <div class="backup-actions">
                                    <button class="btn btn-primary btn-sm">恢复</button>
                                    <button class="btn btn-success btn-sm">下载</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                </div>
                            </div>
                            <div class="backup-item">
                                <div class="backup-info">
                                    <div class="backup-name">数据库备份_20240623</div>
                                    <div class="backup-meta">大小: 89MB | 类型: 数据库备份 | 状态: 成功</div>
                                </div>
                                <div class="backup-actions">
                                    <button class="btn btn-primary btn-sm">恢复</button>
                                    <button class="btn btn-success btn-sm">下载</button>
                                    <button class="btn btn-danger btn-sm">删除</button>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn btn-primary" style="padding: 12px 40px; margin-right: 15px;">
                                <i class="fas fa-play"></i> 立即备份
                            </button>
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div class="tab-content" id="security-settings">
                    <div class="settings-form">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-shield-alt"></i>
                                安全策略
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">登录失败锁定</label>
                                    <select class="form-control">
                                        <option>5次失败锁定</option>
                                        <option>3次失败锁定</option>
                                        <option>10次失败锁定</option>
                                        <option>不锁定</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">锁定时间</label>
                                    <select class="form-control">
                                        <option>15分钟</option>
                                        <option>30分钟</option>
                                        <option>1小时</option>
                                        <option>24小时</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">双因子认证</label>
                                    <select class="form-control">
                                        <option>强制开启</option>
                                        <option>可选开启</option>
                                        <option>关闭</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">IP白名单</label>
                                    <select class="form-control">
                                        <option>启用</option>
                                        <option>禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 系统监控 -->
                <div class="tab-content" id="system-monitor">
                    <div class="monitor-grid">
                        <div class="monitor-card">
                            <div class="monitor-icon">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="monitor-value">45.2%</div>
                            <div class="monitor-label">CPU使用率</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-icon">
                                <i class="fas fa-memory"></i>
                            </div>
                            <div class="monitor-value">68.5%</div>
                            <div class="monitor-label">内存使用率</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-icon">
                                <i class="fas fa-hdd"></i>
                            </div>
                            <div class="monitor-value">23.8%</div>
                            <div class="monitor-label">磁盘使用率</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="monitor-value">156MB/s</div>
                            <div class="monitor-label">网络流量</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="monitor-value">1,234</div>
                            <div class="monitor-label">在线用户</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="monitor-value">
                                <span class="status-indicator status-online"></span>正常
                            </div>
                            <div class="monitor-label">服务状态</div>
                        </div>
                    </div>
                </div>

                <!-- 日志管理 -->
                <div class="tab-content" id="log-management">
                    <table class="log-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>级别</th>
                                <th>模块</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-06-25 14:30:25</td>
                                <td><span class="log-level level-info">INFO</span></td>
                                <td>用户管理</td>
                                <td>admin</td>
                                <td>用户登录</td>
                                <td>管理员登录系统</td>
                            </tr>
                            <tr>
                                <td>2024-06-25 14:28:15</td>
                                <td><span class="log-level level-warning">WARN</span></td>
                                <td>系统监控</td>
                                <td>system</td>
                                <td>性能警告</td>
                                <td>CPU使用率超过80%</td>
                            </tr>
                            <tr>
                                <td>2024-06-25 14:25:10</td>
                                <td><span class="log-level level-error">ERROR</span></td>
                                <td>数据库</td>
                                <td>system</td>
                                <td>连接失败</td>
                                <td>数据库连接超时</td>
                            </tr>
                            <tr>
                                <td>2024-06-25 14:20:05</td>
                                <td><span class="log-level level-info">INFO</span></td>
                                <td>学生管理</td>
                                <td>teacher01</td>
                                <td>数据更新</td>
                                <td>更新学生成绩信息</td>
                            </tr>
                            <tr>
                                <td>2024-06-25 14:15:30</td>
                                <td><span class="log-level level-info">INFO</span></td>
                                <td>课程管理</td>
                                <td>admin</td>
                                <td>课程创建</td>
                                <td>创建新课程：高等数学</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('系统设置');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 权限切换
        document.querySelectorAll('.permission-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
                const permissionName = this.parentElement.querySelector('.permission-name').textContent;
                const status = this.classList.contains('active') ? '启用' : '禁用';
                SmartCampus.showMessage(`${permissionName}权限已${status}`, 'success');
            });
        });

        // 备份操作
        document.querySelectorAll('.backup-actions .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.textContent.trim();
                const backupName = this.closest('.backup-item').querySelector('.backup-name').textContent;

                if (action === '恢复') {
                    if (confirm(`确定要恢复备份"${backupName}"吗？`)) {
                        SmartCampus.showMessage('备份恢复中...', 'info');
                    }
                } else if (action === '下载') {
                    SmartCampus.showMessage('开始下载备份文件...', 'success');
                } else if (action === '删除') {
                    if (confirm(`确定要删除备份"${backupName}"吗？`)) {
                        SmartCampus.showMessage('备份已删除', 'success');
                        this.closest('.backup-item').remove();
                    }
                }
            });
        });

        // 保存设置按钮
        document.querySelectorAll('.btn-success').forEach(btn => {
            if (btn.textContent.includes('保存')) {
                btn.addEventListener('click', function() {
                    SmartCampus.showMessage('设置保存成功！', 'success');
                });
            }
        });

        // 立即备份按钮
        document.querySelector('.btn-primary').addEventListener('click', function() {
            if (this.textContent.includes('立即备份')) {
                SmartCampus.showMessage('开始执行备份...', 'info');
                setTimeout(() => {
                    SmartCampus.showMessage('备份完成！', 'success');
                }, 3000);
            }
        });

        // 监控卡片点击
        document.querySelectorAll('.monitor-card').forEach(card => {
            card.addEventListener('click', function() {
                const label = this.querySelector('.monitor-label').textContent;
                SmartCampus.showMessage(`查看${label}详细信息...`, 'info');
            });
        });

        // 日志表格行点击
        document.querySelectorAll('.log-table tbody tr').forEach(row => {
            row.addEventListener('click', function() {
                SmartCampus.showMessage('查看日志详情...', 'info');
            });
        });

        // 模拟实时监控数据更新
        setInterval(() => {
            const cpuCard = document.querySelector('.monitor-card:nth-child(1) .monitor-value');
            const memoryCard = document.querySelector('.monitor-card:nth-child(2) .monitor-value');

            if (cpuCard && memoryCard) {
                const newCpu = (Math.random() * 100).toFixed(1);
                const newMemory = (Math.random() * 100).toFixed(1);

                cpuCard.textContent = newCpu + '%';
                memoryCard.textContent = newMemory + '%';
            }
        }, 5000);
    </script>
</body>
</html>