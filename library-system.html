<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图书资源中心 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .library-header {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }

        .library-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .library-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #2ecc71;
            background: linear-gradient(135deg, rgba(46,204,113,0.1), rgba(39,174,96,0.1));
            border-bottom-color: #2ecc71;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .search-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: end;
        }

        .advanced-search {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .book-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .book-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            display: flex;
            gap: 15px;
        }

        .book-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .book-cover {
            width: 80px;
            height: 120px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            flex-shrink: 0;
        }

        .book-info {
            flex: 1;
        }

        .book-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .book-meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .book-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-top: 10px;
        }

        .status-available {
            background: #d4edda;
            color: #155724;
        }

        .status-borrowed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-reserved {
            background: #fff3cd;
            color: #856404;
        }

        .book-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .borrow-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .borrow-table th {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .borrow-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .borrow-table tr:hover {
            background: #f8f9fa;
        }

        .reader-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .reader-info {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .reader-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .reader-details h3 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .reader-meta {
            color: #666;
            font-size: 14px;
        }

        .reading-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .reading-stat {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .reading-number {
            font-size: 20px;
            font-weight: bold;
            color: #2ecc71;
            margin-bottom: 5px;
        }

        .reading-label {
            font-size: 12px;
            color: #666;
        }

        .ebook-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .ebook-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
            transition: all 0.3s ease;
        }

        .ebook-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .ebook-icon {
            font-size: 48px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .ebook-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .ebook-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .reservation-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .analytics-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .popular-books {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .popular-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .popular-rank {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .popular-info {
            flex: 1;
        }

        .popular-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
        }

        .popular-author {
            color: #666;
            font-size: 12px;
        }

        .popular-count {
            color: #2ecc71;
            font-weight: 500;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <!-- 图书馆头部 -->
                <div class="library-header">
                    <h1 style="margin: 0 0 10px 0; font-size: 28px;">
                        <i class="fas fa-book-open"></i> 图书资源中心
                    </h1>
                    <p style="margin: 0; opacity: 0.9;">知识的海洋，智慧的源泉</p>
                </div>

                <!-- 图书馆统计 -->
                <div class="library-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-books"></i>
                        </div>
                        <div class="stat-number">125,680</div>
                        <div class="stat-label">馆藏图书</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tablet-alt"></i>
                        </div>
                        <div class="stat-number">8,456</div>
                        <div class="stat-label">电子图书</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">2,340</div>
                        <div class="stat-label">注册读者</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stat-number">1,567</div>
                        <div class="stat-label">今日借还</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">在线服务</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="library-tabs">
                    <button class="tab-btn active" data-tab="book-search">
                        <i class="fas fa-search"></i> 图书检索
                    </button>
                    <button class="tab-btn" data-tab="borrow-return">
                        <i class="fas fa-exchange-alt"></i> 借还管理
                    </button>
                    <button class="tab-btn" data-tab="reader-management">
                        <i class="fas fa-user-friends"></i> 读者管理
                    </button>
                    <button class="tab-btn" data-tab="online-reservation">
                        <i class="fas fa-calendar-check"></i> 在线预约
                    </button>
                    <button class="tab-btn" data-tab="digital-library">
                        <i class="fas fa-laptop"></i> 电子图书馆
                    </button>
                    <button class="tab-btn" data-tab="reading-analytics">
                        <i class="fas fa-chart-bar"></i> 阅读统计
                    </button>
                </div>

                <!-- 图书检索 -->
                <div class="tab-content active" id="book-search">
                    <div class="search-section">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-search"></i> 图书检索系统
                        </h3>
                        <div class="search-form">
                            <div class="form-group">
                                <input type="text" class="form-control" placeholder="请输入书名、作者、ISBN或关键词..." style="font-size: 16px; padding: 12px;">
                            </div>
                            <div class="form-group">
                                <select class="form-control">
                                    <option>全部分类</option>
                                    <option>文学</option>
                                    <option>科学技术</option>
                                    <option>社会科学</option>
                                    <option>艺术</option>
                                    <option>历史地理</option>
                                    <option>哲学宗教</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" style="padding: 12px 30px;">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>

                        <div class="advanced-search" style="display: none;" id="advanced-search">
                            <div class="form-group">
                                <label class="form-label">作者</label>
                                <input type="text" class="form-control" placeholder="作者姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">出版社</label>
                                <input type="text" class="form-control" placeholder="出版社名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">出版年份</label>
                                <select class="form-control">
                                    <option>不限</option>
                                    <option>2024年</option>
                                    <option>2023年</option>
                                    <option>2022年</option>
                                    <option>2021年</option>
                                    <option>2020年及以前</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">语言</label>
                                <select class="form-control">
                                    <option>不限</option>
                                    <option>中文</option>
                                    <option>英文</option>
                                    <option>其他</option>
                                </select>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 15px;">
                            <button class="btn btn-secondary" onclick="toggleAdvancedSearch()">
                                <i class="fas fa-cog"></i> 高级搜索
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">搜索结果</h3>
                        </div>
                        <div class="card-body">
                            <div class="book-grid">
                                <div class="book-card">
                                    <div class="book-cover">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="book-info">
                                        <div class="book-title">人工智能：一种现代的方法</div>
                                        <div class="book-meta">作者：Stuart Russell, Peter Norvig</div>
                                        <div class="book-meta">出版社：清华大学出版社</div>
                                        <div class="book-meta">ISBN：978-7-302-12345-6</div>
                                        <div class="book-meta">索书号：TP18/R123</div>
                                        <div class="book-status status-available">可借阅</div>
                                        <div class="book-actions">
                                            <button class="btn btn-primary" style="padding: 5px 15px; font-size: 12px;">借阅</button>
                                            <button class="btn btn-secondary" style="padding: 5px 15px; font-size: 12px;">预约</button>
                                            <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">详情</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="book-card">
                                    <div class="book-cover">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="book-info">
                                        <div class="book-title">深度学习</div>
                                        <div class="book-meta">作者：Ian Goodfellow, Yoshua Bengio</div>
                                        <div class="book-meta">出版社：人民邮电出版社</div>
                                        <div class="book-meta">ISBN：978-7-115-46187-7</div>
                                        <div class="book-meta">索书号：TP181/G456</div>
                                        <div class="book-status status-borrowed">已借出</div>
                                        <div class="book-actions">
                                            <button class="btn btn-warning" style="padding: 5px 15px; font-size: 12px;">预约</button>
                                            <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">详情</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="book-card">
                                    <div class="book-cover">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="book-info">
                                        <div class="book-title">机器学习实战</div>
                                        <div class="book-meta">作者：Peter Harrington</div>
                                        <div class="book-meta">出版社：人民邮电出版社</div>
                                        <div class="book-meta">ISBN：978-7-115-27766-1</div>
                                        <div class="book-meta">索书号：TP181/H234</div>
                                        <div class="book-status status-reserved">已预约</div>
                                        <div class="book-actions">
                                            <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">详情</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="book-card">
                                    <div class="book-cover">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="book-info">
                                        <div class="book-title">Python机器学习</div>
                                        <div class="book-meta">作者：Sebastian Raschka</div>
                                        <div class="book-meta">出版社：机械工业出版社</div>
                                        <div class="book-meta">ISBN：978-7-111-54332-1</div>
                                        <div class="book-meta">索书号：TP311.56/R234</div>
                                        <div class="book-status status-available">可借阅</div>
                                        <div class="book-actions">
                                            <button class="btn btn-primary" style="padding: 5px 15px; font-size: 12px;">借阅</button>
                                            <button class="btn btn-secondary" style="padding: 5px 15px; font-size: 12px;">预约</button>
                                            <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">详情</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 借还管理 -->
                <div class="tab-content" id="borrow-return">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">图书借还管理</h3>
                        </div>
                        <div class="card-body">
                            <table class="borrow-table">
                                <thead>
                                    <tr>
                                        <th>读者姓名</th>
                                        <th>学号/工号</th>
                                        <th>图书名称</th>
                                        <th>借阅日期</th>
                                        <th>应还日期</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>张三</td>
                                        <td>2024001001</td>
                                        <td>人工智能：一种现代的方法</td>
                                        <td>2024-06-01</td>
                                        <td>2024-07-01</td>
                                        <td><span class="book-status status-available">正常</span></td>
                                        <td>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">还书</button>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">续借</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>李四</td>
                                        <td>2024001002</td>
                                        <td>深度学习</td>
                                        <td>2024-05-15</td>
                                        <td>2024-06-15</td>
                                        <td><span class="book-status status-borrowed">逾期</span></td>
                                        <td>
                                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">催还</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">还书</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>王五</td>
                                        <td>T2024001</td>
                                        <td>机器学习实战</td>
                                        <td>2024-06-10</td>
                                        <td>2024-07-10</td>
                                        <td><span class="book-status status-available">正常</span></td>
                                        <td>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">还书</button>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">续借</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>赵六</td>
                                        <td>2024001003</td>
                                        <td>Python机器学习</td>
                                        <td>2024-06-20</td>
                                        <td>2024-07-20</td>
                                        <td><span class="book-status status-available">正常</span></td>
                                        <td>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">还书</button>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">续借</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 读者管理 -->
                <div class="tab-content" id="reader-management">
                    <div class="reader-card">
                        <div class="reader-info">
                            <div class="reader-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="reader-details">
                                <h3>张三 - 学生读者</h3>
                                <div class="reader-meta">
                                    学号：2024001001 | 计算机科学与技术学院 | 本科生<br>
                                    注册时间：2024年9月1日 | 有效期至：2028年6月30日
                                </div>
                            </div>
                        </div>
                        <div class="reading-stats">
                            <div class="reading-stat">
                                <div class="reading-number">15</div>
                                <div class="reading-label">当前借阅</div>
                            </div>
                            <div class="reading-stat">
                                <div class="reading-number">156</div>
                                <div class="reading-label">累计借阅</div>
                            </div>
                            <div class="reading-stat">
                                <div class="reading-number">0</div>
                                <div class="reading-label">逾期次数</div>
                            </div>
                            <div class="reading-stat">
                                <div class="reading-number">¥0</div>
                                <div class="reading-label">欠费金额</div>
                            </div>
                            <div class="reading-stat">
                                <div class="reading-number">5</div>
                                <div class="reading-label">预约图书</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">读者借阅历史</h3>
                        </div>
                        <div class="card-body">
                            <table class="borrow-table">
                                <thead>
                                    <tr>
                                        <th>图书名称</th>
                                        <th>作者</th>
                                        <th>借阅日期</th>
                                        <th>归还日期</th>
                                        <th>借阅天数</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>人工智能：一种现代的方法</td>
                                        <td>Stuart Russell</td>
                                        <td>2024-06-01</td>
                                        <td>-</td>
                                        <td>24天</td>
                                        <td><span class="book-status status-borrowed">借阅中</span></td>
                                    </tr>
                                    <tr>
                                        <td>算法导论</td>
                                        <td>Thomas H. Cormen</td>
                                        <td>2024-05-01</td>
                                        <td>2024-05-28</td>
                                        <td>27天</td>
                                        <td><span class="book-status status-available">已归还</span></td>
                                    </tr>
                                    <tr>
                                        <td>数据结构与算法分析</td>
                                        <td>Mark Allen Weiss</td>
                                        <td>2024-04-15</td>
                                        <td>2024-05-10</td>
                                        <td>25天</td>
                                        <td><span class="book-status status-available">已归还</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 在线预约 -->
                <div class="tab-content" id="online-reservation">
                    <div class="reservation-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-calendar-check"></i> 图书预约申请
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">读者信息</label>
                                <input type="text" class="form-control" placeholder="学号/工号" readonly value="2024001001">
                            </div>
                            <div class="form-group">
                                <label class="form-label">读者姓名</label>
                                <input type="text" class="form-control" placeholder="姓名" readonly value="张三">
                            </div>
                            <div class="form-group">
                                <label class="form-label">图书ISBN</label>
                                <input type="text" class="form-control" placeholder="请输入图书ISBN">
                            </div>
                            <div class="form-group">
                                <label class="form-label">图书名称</label>
                                <input type="text" class="form-control" placeholder="请输入图书名称">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">预约类型</label>
                                <select class="form-control">
                                    <option>普通预约</option>
                                    <option>紧急预约</option>
                                    <option>教学用书预约</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">预约期限</label>
                                <select class="form-control">
                                    <option>7天</option>
                                    <option>14天</option>
                                    <option>30天</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话</label>
                                <input type="tel" class="form-control" placeholder="请输入联系电话">
                            </div>
                            <div class="form-group">
                                <label class="form-label">电子邮箱</label>
                                <input type="email" class="form-control" placeholder="请输入电子邮箱">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">预约说明</label>
                            <textarea class="form-control" rows="3" placeholder="请说明预约原因或特殊要求..."></textarea>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-check"></i> 提交预约
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">我的预约记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="borrow-table">
                                <thead>
                                    <tr>
                                        <th>图书名称</th>
                                        <th>预约时间</th>
                                        <th>预约类型</th>
                                        <th>状态</th>
                                        <th>到书通知</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>深度学习</td>
                                        <td>2024-06-20</td>
                                        <td>普通预约</td>
                                        <td><span class="book-status status-reserved">等待中</span></td>
                                        <td>短信+邮件</td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">取消</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>机器学习实战</td>
                                        <td>2024-06-18</td>
                                        <td>教学用书预约</td>
                                        <td><span class="book-status status-available">已到书</span></td>
                                        <td>已通知</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">借阅</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 电子图书馆 -->
                <div class="tab-content" id="digital-library">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">电子图书馆</h3>
                        </div>
                        <div class="card-body">
                            <div class="ebook-grid">
                                <div class="ebook-card">
                                    <div class="ebook-icon">
                                        <i class="fas fa-file-pdf"></i>
                                    </div>
                                    <div class="ebook-title">学术期刊数据库</div>
                                    <div class="ebook-desc">包含CNKI、万方、维普等主要学术期刊数据库</div>
                                    <button class="btn btn-primary">访问数据库</button>
                                </div>

                                <div class="ebook-card">
                                    <div class="ebook-icon">
                                        <i class="fas fa-book-reader"></i>
                                    </div>
                                    <div class="ebook-title">电子图书阅读</div>
                                    <div class="ebook-desc">超过10万册电子图书在线阅读</div>
                                    <button class="btn btn-primary">开始阅读</button>
                                </div>

                                <div class="ebook-card">
                                    <div class="ebook-icon">
                                        <i class="fas fa-video"></i>
                                    </div>
                                    <div class="ebook-title">多媒体资源</div>
                                    <div class="ebook-desc">教学视频、音频资料、课件等</div>
                                    <button class="btn btn-primary">浏览资源</button>
                                </div>

                                <div class="ebook-card">
                                    <div class="ebook-icon">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <div class="ebook-title">学位论文库</div>
                                    <div class="ebook-desc">博士、硕士学位论文全文数据库</div>
                                    <button class="btn btn-primary">查看论文</button>
                                </div>

                                <div class="ebook-card">
                                    <div class="ebook-icon">
                                        <i class="fas fa-newspaper"></i>
                                    </div>
                                    <div class="ebook-title">报纸期刊</div>
                                    <div class="ebook-desc">国内外重要报纸期刊电子版</div>
                                    <button class="btn btn-primary">在线阅读</button>
                                </div>

                                <div class="ebook-card">
                                    <div class="ebook-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="ebook-title">外文数据库</div>
                                    <div class="ebook-desc">IEEE、ACM、Springer等外文数据库</div>
                                    <button class="btn btn-primary">访问资源</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 阅读统计 -->
                <div class="tab-content" id="reading-analytics">
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <h4 style="margin-bottom: 15px;">月度借阅趋势</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line" style="font-size: 24px; margin-right: 10px;"></i>
                                借阅趋势图表
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h4 style="margin-bottom: 15px;">图书分类统计</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-pie" style="font-size: 24px; margin-right: 10px;"></i>
                                分类统计图表
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h4 style="margin-bottom: 15px;">读者活跃度</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-bar" style="font-size: 24px; margin-right: 10px;"></i>
                                活跃度统计图表
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h4 style="margin-bottom: 15px;">馆藏利用率</h4>
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-area" style="font-size: 24px; margin-right: 10px;"></i>
                                利用率分析图表
                            </div>
                        </div>
                    </div>

                    <div class="popular-books">
                        <h4 style="margin-bottom: 20px;">热门图书排行榜</h4>
                        <div class="popular-item">
                            <div class="popular-rank">1</div>
                            <div class="popular-info">
                                <div class="popular-title">人工智能：一种现代的方法</div>
                                <div class="popular-author">Stuart Russell, Peter Norvig</div>
                            </div>
                            <div class="popular-count">借阅 156 次</div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">2</div>
                            <div class="popular-info">
                                <div class="popular-title">深度学习</div>
                                <div class="popular-author">Ian Goodfellow</div>
                            </div>
                            <div class="popular-count">借阅 142 次</div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">3</div>
                            <div class="popular-info">
                                <div class="popular-title">算法导论</div>
                                <div class="popular-author">Thomas H. Cormen</div>
                            </div>
                            <div class="popular-count">借阅 128 次</div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">4</div>
                            <div class="popular-info">
                                <div class="popular-title">机器学习实战</div>
                                <div class="popular-author">Peter Harrington</div>
                            </div>
                            <div class="popular-count">借阅 115 次</div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">5</div>
                            <div class="popular-info">
                                <div class="popular-title">Python机器学习</div>
                                <div class="popular-author">Sebastian Raschka</div>
                            </div>
                            <div class="popular-count">借阅 98 次</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('图书资源');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 高级搜索切换
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advanced-search');
            if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                advancedSearch.style.display = 'grid';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        // 图书搜索
        document.querySelector('.search-form .btn-primary').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('搜索完成，找到相关图书 156 本', 'success');
            }, 1000);
        });

        // 图书操作
        document.querySelectorAll('.book-actions .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`正在执行${action}操作...`, 'info');
            });
        });

        // 借还操作
        document.querySelectorAll('.borrow-table .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`正在执行${action}操作...`, 'info');
            });
        });

        // 预约提交
        document.querySelector('.reservation-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('预约申请提交成功！', 'success');
            }, 1000);
        });

        // 电子资源访问
        document.querySelectorAll('.ebook-card .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                SmartCampus.showMessage('正在跳转到电子资源...', 'info');
            });
        });

        // 热门图书点击
        document.querySelectorAll('.popular-item').forEach(item => {
            item.addEventListener('click', function() {
                SmartCampus.showMessage('查看图书详情...', 'info');
            });
        });
    </script>
</body>
</html>
