<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心理健康咨询 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .health-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }

        .health-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .health-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .health-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .assessment-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .assessment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .assessment-icon {
            font-size: 48px;
            margin-bottom: 20px;
            text-align: center;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .assessment-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
            text-align: center;
        }

        .assessment-desc {
            color: #666;
            text-align: center;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .assessment-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
        }

        .assessment-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .assessment-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(52,152,219,0.3);
        }

        .counselor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .counselor-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }

        .counselor-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin: 0 auto 15px;
        }

        .counselor-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .counselor-title {
            color: #666;
            margin-bottom: 10px;
        }

        .counselor-speciality {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            color: #555;
            margin: 5px;
            display: inline-block;
        }

        .counselor-rating {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            margin: 15px 0;
        }

        .rating-stars {
            color: #f39c12;
        }

        .appointment-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .time-slots {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .time-slot {
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .time-slot:hover {
            border-color: #3498db;
            background: rgba(52,152,219,0.1);
        }

        .time-slot.selected {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }

        .time-slot.unavailable {
            background: #f8f9fa;
            color: #999;
            cursor: not-allowed;
        }

        .records-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .records-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .records-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .records-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-scheduled {
            background: #cce5ff;
            color: #004085;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .health-tips {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .tips-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tips-list {
            list-style: none;
            padding: 0;
        }

        .tips-list li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .tips-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: rgba(255,255,255,0.8);
            font-weight: bold;
        }

        .emergency-contact {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .emergency-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .emergency-phone {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .emergency-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        .mood-tracker {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .mood-options {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }

        .mood-option {
            text-align: center;
            cursor: pointer;
            padding: 15px;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .mood-option:hover {
            background: #f8f9fa;
        }

        .mood-option.selected {
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border: 2px solid #3498db;
        }

        .mood-emoji {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .mood-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <div class="health-header">
                    <div class="health-title">
                        <i class="fas fa-heart"></i> 心理健康咨询中心
                    </div>
                    <div class="health-subtitle">关爱心理健康，守护美好心灵</div>
                </div>

                <!-- 紧急联系 -->
                <div class="emergency-contact">
                    <div class="emergency-title">
                        <i class="fas fa-phone-alt"></i> 24小时心理危机干预热线
                    </div>
                    <div class="emergency-phone">************</div>
                    <div class="emergency-desc">如遇紧急情况，请立即拨打热线电话</div>
                </div>

                <!-- 功能选项卡 -->
                <div class="health-tabs">
                    <button class="tab-btn active" data-tab="assessment">
                        <i class="fas fa-clipboard-check"></i> 心理测评
                    </button>
                    <button class="tab-btn" data-tab="counseling">
                        <i class="fas fa-user-md"></i> 咨询预约
                    </button>
                    <button class="tab-btn" data-tab="records">
                        <i class="fas fa-history"></i> 咨询记录
                    </button>
                    <button class="tab-btn" data-tab="mood-tracker">
                        <i class="fas fa-smile"></i> 心情记录
                    </button>
                </div>

                <!-- 心理测评 -->
                <div class="tab-content active" id="assessment">
                    <div class="assessment-grid">
                        <div class="assessment-card" onclick="startAssessment('scl90')">
                            <div class="assessment-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="assessment-title">SCL-90症状自评量表</div>
                            <div class="assessment-desc">
                                评估心理症状的严重程度，包括躯体化、强迫症状、人际关系敏感等9个因子
                            </div>
                            <div class="assessment-info">
                                <span>90题 · 约15分钟</span>
                                <span>已测试 1,245 人</span>
                            </div>
                            <button class="assessment-btn">开始测评</button>
                        </div>

                        <div class="assessment-card" onclick="startAssessment('sas')">
                            <div class="assessment-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="assessment-title">SAS焦虑自评量表</div>
                            <div class="assessment-desc">
                                评估焦虑症状的严重程度，帮助识别焦虑情绪和相关症状
                            </div>
                            <div class="assessment-info">
                                <span>20题 · 约5分钟</span>
                                <span>已测试 2,156 人</span>
                            </div>
                            <button class="assessment-btn">开始测评</button>
                        </div>

                        <div class="assessment-card" onclick="startAssessment('sds')">
                            <div class="assessment-icon">
                                <i class="fas fa-cloud-rain"></i>
                            </div>
                            <div class="assessment-title">SDS抑郁自评量表</div>
                            <div class="assessment-desc">
                                评估抑郁症状的严重程度，帮助识别抑郁情绪和相关症状
                            </div>
                            <div class="assessment-info">
                                <span>20题 · 约5分钟</span>
                                <span>已测试 1,876 人</span>
                            </div>
                            <button class="assessment-btn">开始测评</button>
                        </div>

                        <div class="assessment-card" onclick="startAssessment('mbti')">
                            <div class="assessment-icon">
                                <i class="fas fa-user-friends"></i>
                            </div>
                            <div class="assessment-title">MBTI人格类型测试</div>
                            <div class="assessment-desc">
                                了解个人性格特点和行为偏好，帮助更好地认识自己
                            </div>
                            <div class="assessment-info">
                                <span>93题 · 约20分钟</span>
                                <span>已测试 3,421 人</span>
                            </div>
                            <button class="assessment-btn">开始测评</button>
                        </div>

                        <div class="assessment-card" onclick="startAssessment('stress')">
                            <div class="assessment-icon">
                                <i class="fas fa-weight-hanging"></i>
                            </div>
                            <div class="assessment-title">压力评估量表</div>
                            <div class="assessment-desc">
                                评估当前生活和学习中的压力水平，提供针对性建议
                            </div>
                            <div class="assessment-info">
                                <span>25题 · 约8分钟</span>
                                <span>已测试 1,654 人</span>
                            </div>
                            <button class="assessment-btn">开始测评</button>
                        </div>

                        <div class="assessment-card" onclick="startAssessment('sleep')">
                            <div class="assessment-icon">
                                <i class="fas fa-bed"></i>
                            </div>
                            <div class="assessment-title">睡眠质量评估</div>
                            <div class="assessment-desc">
                                评估睡眠质量和睡眠障碍，提供改善睡眠的建议
                            </div>
                            <div class="assessment-info">
                                <span>18题 · 约6分钟</span>
                                <span>已测试 987 人</span>
                            </div>
                            <button class="assessment-btn">开始测评</button>
                        </div>
                    </div>

                    <div class="health-tips">
                        <div class="tips-title">
                            <i class="fas fa-lightbulb"></i> 心理健康小贴士
                        </div>
                        <ul class="tips-list">
                            <li>保持规律的作息时间，充足的睡眠有助于心理健康</li>
                            <li>适度运动可以释放压力，改善情绪状态</li>
                            <li>学会倾诉，与朋友、家人分享内心感受</li>
                            <li>培养兴趣爱好，丰富精神生活</li>
                            <li>学习放松技巧，如深呼吸、冥想等</li>
                            <li>遇到困难时，及时寻求专业帮助</li>
                        </ul>
                    </div>
                </div>

                <!-- 咨询预约 -->
                <div class="tab-content" id="counseling">
                    <div class="counselor-grid">
                        <div class="counselor-card">
                            <div class="counselor-avatar">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="counselor-name">李心理师</div>
                            <div class="counselor-title">国家二级心理咨询师</div>
                            <div class="counselor-rating">
                                <span class="rating-stars">★★★★★</span>
                                <span>(4.9分)</span>
                            </div>
                            <div>
                                <span class="counselor-speciality">焦虑抑郁</span>
                                <span class="counselor-speciality">人际关系</span>
                                <span class="counselor-speciality">学业压力</span>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" onclick="bookAppointment('李心理师')">
                                    <i class="fas fa-calendar-plus"></i> 预约咨询
                                </button>
                            </div>
                        </div>

                        <div class="counselor-card">
                            <div class="counselor-avatar">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="counselor-name">王心理师</div>
                            <div class="counselor-title">国家二级心理咨询师</div>
                            <div class="counselor-rating">
                                <span class="rating-stars">★★★★☆</span>
                                <span>(4.7分)</span>
                            </div>
                            <div>
                                <span class="counselor-speciality">情感问题</span>
                                <span class="counselor-speciality">自我认知</span>
                                <span class="counselor-speciality">职业规划</span>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" onclick="bookAppointment('王心理师')">
                                    <i class="fas fa-calendar-plus"></i> 预约咨询
                                </button>
                            </div>
                        </div>

                        <div class="counselor-card">
                            <div class="counselor-avatar">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="counselor-name">张心理师</div>
                            <div class="counselor-title">国家三级心理咨询师</div>
                            <div class="counselor-rating">
                                <span class="rating-stars">★★★★★</span>
                                <span>(4.8分)</span>
                            </div>
                            <div>
                                <span class="counselor-speciality">学习困难</span>
                                <span class="counselor-speciality">考试焦虑</span>
                                <span class="counselor-speciality">时间管理</span>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" onclick="bookAppointment('张心理师')">
                                    <i class="fas fa-calendar-plus"></i> 预约咨询
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="appointment-form" id="appointment-form" style="display: none;">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-calendar-plus"></i> 预约咨询 - <span id="selected-counselor"></span>
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">咨询类型</label>
                                <select class="form-control">
                                    <option>个体咨询</option>
                                    <option>团体咨询</option>
                                    <option>在线咨询</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">预约日期</label>
                                <input type="date" class="form-control" min="2024-06-26">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">可选时间段</label>
                            <div class="time-slots">
                                <div class="time-slot">09:00-10:00</div>
                                <div class="time-slot">10:00-11:00</div>
                                <div class="time-slot unavailable">11:00-12:00</div>
                                <div class="time-slot">14:00-15:00</div>
                                <div class="time-slot">15:00-16:00</div>
                                <div class="time-slot unavailable">16:00-17:00</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询主题</label>
                            <input type="text" class="form-control" placeholder="请简要描述您希望咨询的问题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">详细描述（选填）</label>
                            <textarea class="form-control" rows="4" placeholder="请详细描述您的困扰或问题，有助于咨询师更好地了解您的情况"></textarea>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-check"></i> 确认预约
                            </button>
                            <button class="btn btn-secondary" style="padding: 12px 40px; margin-left: 10px;" onclick="cancelAppointment()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 咨询记录 -->
                <div class="tab-content" id="records">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">咨询记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="records-table">
                                <thead>
                                    <tr>
                                        <th>预约时间</th>
                                        <th>咨询师</th>
                                        <th>咨询类型</th>
                                        <th>咨询主题</th>
                                        <th>状态</th>
                                        <th>满意度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-06-20 14:00</td>
                                        <td>李心理师</td>
                                        <td>个体咨询</td>
                                        <td>学业压力</td>
                                        <td><span class="status-badge status-completed">已完成</span></td>
                                        <td>★★★★★</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 10:00</td>
                                        <td>王心理师</td>
                                        <td>在线咨询</td>
                                        <td>人际关系</td>
                                        <td><span class="status-badge status-scheduled">已预约</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">修改</button>
                                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">取消</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-15 15:00</td>
                                        <td>张心理师</td>
                                        <td>个体咨询</td>
                                        <td>考试焦虑</td>
                                        <td><span class="status-badge status-cancelled">已取消</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">重新预约</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 心情记录 -->
                <div class="tab-content" id="mood-tracker">
                    <div class="mood-tracker">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-smile"></i> 今日心情记录
                        </h3>
                        <p style="color: #666; margin-bottom: 20px;">记录您今天的心情状态，有助于了解情绪变化规律</p>

                        <div class="mood-options">
                            <div class="mood-option" data-mood="very-happy">
                                <div class="mood-emoji">😄</div>
                                <div class="mood-label">非常开心</div>
                            </div>
                            <div class="mood-option" data-mood="happy">
                                <div class="mood-emoji">😊</div>
                                <div class="mood-label">开心</div>
                            </div>
                            <div class="mood-option" data-mood="neutral">
                                <div class="mood-emoji">😐</div>
                                <div class="mood-label">一般</div>
                            </div>
                            <div class="mood-option" data-mood="sad">
                                <div class="mood-emoji">😔</div>
                                <div class="mood-label">难过</div>
                            </div>
                            <div class="mood-option" data-mood="very-sad">
                                <div class="mood-emoji">😢</div>
                                <div class="mood-label">非常难过</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">心情描述（选填）</label>
                            <textarea class="form-control" rows="3" placeholder="描述一下今天的心情和感受..."></textarea>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-success">
                                <i class="fas fa-save"></i> 保存心情记录
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">心情统计</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 16px;">
                                <i class="fas fa-chart-line" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>心情变化趋势图表加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('学生管理 > 心理健康咨询');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 开始测评
        function startAssessment(type) {
            const assessmentNames = {
                'scl90': 'SCL-90症状自评量表',
                'sas': 'SAS焦虑自评量表',
                'sds': 'SDS抑郁自评量表',
                'mbti': 'MBTI人格类型测试',
                'stress': '压力评估量表',
                'sleep': '睡眠质量评估'
            };

            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage(`正在加载${assessmentNames[type]}...`, 'info');
            }, 1000);
        }

        // 预约咨询
        function bookAppointment(counselor) {
            document.getElementById('selected-counselor').textContent = counselor;
            document.getElementById('appointment-form').style.display = 'block';
            document.getElementById('appointment-form').scrollIntoView({ behavior: 'smooth' });
        }

        // 取消预约
        function cancelAppointment() {
            document.getElementById('appointment-form').style.display = 'none';
        }

        // 时间段选择
        document.querySelectorAll('.time-slot').forEach(slot => {
            slot.addEventListener('click', function() {
                if (!this.classList.contains('unavailable')) {
                    document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));
                    this.classList.add('selected');
                }
            });
        });

        // 心情选择
        document.querySelectorAll('.mood-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.mood-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 确认预约
        document.querySelector('#counseling .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('预约成功！咨询师将在24小时内与您联系确认。', 'success');
                cancelAppointment();
            }, 1500);
        });

        // 保存心情记录
        document.querySelector('#mood-tracker .btn-success').addEventListener('click', function() {
            const selectedMood = document.querySelector('.mood-option.selected');
            if (selectedMood) {
                SmartCampus.showMessage('心情记录保存成功！', 'success');
            } else {
                SmartCampus.showMessage('请先选择今日心情', 'info');
            }
        });
    </script>
</body>
</html>
