<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安防监控系统 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .security-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .security-stat {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .security-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e74c3c, #f39c12);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .security-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #e74c3c;
            background: linear-gradient(135deg, rgba(231,76,60,0.1), rgba(243,156,18,0.1));
            border-bottom-color: #e74c3c;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .camera-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .camera-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .camera-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .camera-view {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            position: relative;
        }

        .camera-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: #2ecc71;
            color: white;
        }

        .status-offline {
            background: #e74c3c;
            color: white;
        }

        .camera-info {
            padding: 15px;
        }

        .camera-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .camera-location {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .camera-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            flex: 1;
            padding: 8px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .control-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .access-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .access-table th {
            background: linear-gradient(135deg, #e74c3c, #f39c12);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .access-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .access-table tr:hover {
            background: #f8f9fa;
        }

        .access-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .access-granted {
            background: #d4edda;
            color: #155724;
        }

        .access-denied {
            background: #f8d7da;
            color: #721c24;
        }

        .alert-panel {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .alert-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-list {
            list-style: none;
            padding: 0;
        }

        .alert-item {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alert-item:last-child {
            border-bottom: none;
        }

        .alert-time {
            font-size: 12px;
            opacity: 0.8;
        }

        .visitor-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .visitor-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .visitor-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .visitor-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .visitor-table tr:hover {
            background: #f8f9fa;
        }

        .visitor-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .status-expired {
            background: #e2e3e5;
            color: #495057;
        }

        .emergency-contact {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .emergency-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .emergency-phone {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .emergency-desc {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-shield-alt"></i> 安防监控系统
                </h1>

                <!-- 紧急联系 -->
                <div class="emergency-contact">
                    <div class="emergency-title">
                        <i class="fas fa-exclamation-triangle"></i> 紧急联系电话
                    </div>
                    <div class="emergency-phone">110 / 119 / 120</div>
                    <div class="emergency-desc">校园安保：6688 | 医务室：6699</div>
                </div>

                <!-- 安防统计 -->
                <div class="security-dashboard">
                    <div class="security-stat">
                        <div class="stat-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">监控摄像头</div>
                    </div>
                    <div class="security-stat">
                        <div class="stat-icon">
                            <i class="fas fa-door-open"></i>
                        </div>
                        <div class="stat-number">48</div>
                        <div class="stat-label">门禁点位</div>
                    </div>
                    <div class="security-stat">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">3</div>
                        <div class="stat-label">今日告警</div>
                    </div>
                    <div class="security-stat">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">1,245</div>
                        <div class="stat-label">今日通行</div>
                    </div>
                </div>

                <!-- 实时告警 -->
                <div class="alert-panel">
                    <div class="alert-title">
                        <i class="fas fa-bell"></i> 实时告警信息
                    </div>
                    <ul class="alert-list">
                        <li class="alert-item">
                            <span>教学楼A座后门异常开启</span>
                            <span class="alert-time">2分钟前</span>
                        </li>
                        <li class="alert-item">
                            <span>停车场区域检测到可疑人员</span>
                            <span class="alert-time">15分钟前</span>
                        </li>
                        <li class="alert-item">
                            <span>宿舍楼1号楼消防通道堵塞</span>
                            <span class="alert-time">1小时前</span>
                        </li>
                    </ul>
                </div>

                <!-- 功能选项卡 -->
                <div class="security-tabs">
                    <button class="tab-btn active" data-tab="monitoring">
                        <i class="fas fa-video"></i> 视频监控
                    </button>
                    <button class="tab-btn" data-tab="access-control">
                        <i class="fas fa-door-open"></i> 门禁管理
                    </button>
                    <button class="tab-btn" data-tab="visitor-management">
                        <i class="fas fa-user-check"></i> 访客管理
                    </button>
                    <button class="tab-btn" data-tab="patrol-records">
                        <i class="fas fa-route"></i> 巡逻记录
                    </button>
                </div>

                <!-- 视频监控 -->
                <div class="tab-content active" id="monitoring">
                    <div class="camera-grid">
                        <div class="camera-card">
                            <div class="camera-view">
                                <div class="camera-status status-online">在线</div>
                                <i class="fas fa-video" style="font-size: 48px; opacity: 0.5;"></i>
                            </div>
                            <div class="camera-info">
                                <div class="camera-name">教学楼A座大门</div>
                                <div class="camera-location">
                                    <i class="fas fa-map-marker-alt"></i> 教学楼A座正门
                                </div>
                                <div class="camera-controls">
                                    <button class="control-btn">
                                        <i class="fas fa-play"></i> 实时
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-history"></i> 回放
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-expand"></i> 全屏
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="camera-card">
                            <div class="camera-view">
                                <div class="camera-status status-online">在线</div>
                                <i class="fas fa-video" style="font-size: 48px; opacity: 0.5;"></i>
                            </div>
                            <div class="camera-info">
                                <div class="camera-name">学生宿舍1号楼</div>
                                <div class="camera-location">
                                    <i class="fas fa-map-marker-alt"></i> 宿舍区1号楼入口
                                </div>
                                <div class="camera-controls">
                                    <button class="control-btn">
                                        <i class="fas fa-play"></i> 实时
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-history"></i> 回放
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-expand"></i> 全屏
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="camera-card">
                            <div class="camera-view">
                                <div class="camera-status status-offline">离线</div>
                                <i class="fas fa-video-slash" style="font-size: 48px; opacity: 0.5;"></i>
                            </div>
                            <div class="camera-info">
                                <div class="camera-name">图书馆主入口</div>
                                <div class="camera-location">
                                    <i class="fas fa-map-marker-alt"></i> 图书馆正门
                                </div>
                                <div class="camera-controls">
                                    <button class="control-btn" disabled>
                                        <i class="fas fa-exclamation-triangle"></i> 维修中
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="camera-card">
                            <div class="camera-view">
                                <div class="camera-status status-online">在线</div>
                                <i class="fas fa-video" style="font-size: 48px; opacity: 0.5;"></i>
                            </div>
                            <div class="camera-info">
                                <div class="camera-name">体育馆周边</div>
                                <div class="camera-location">
                                    <i class="fas fa-map-marker-alt"></i> 体育馆外围
                                </div>
                                <div class="camera-controls">
                                    <button class="control-btn">
                                        <i class="fas fa-play"></i> 实时
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-history"></i> 回放
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-expand"></i> 全屏
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="camera-card">
                            <div class="camera-view">
                                <div class="camera-status status-online">在线</div>
                                <i class="fas fa-video" style="font-size: 48px; opacity: 0.5;"></i>
                            </div>
                            <div class="camera-info">
                                <div class="camera-name">停车场区域</div>
                                <div class="camera-location">
                                    <i class="fas fa-map-marker-alt"></i> 校园停车场
                                </div>
                                <div class="camera-controls">
                                    <button class="control-btn">
                                        <i class="fas fa-play"></i> 实时
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-history"></i> 回放
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-expand"></i> 全屏
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="camera-card">
                            <div class="camera-view">
                                <div class="camera-status status-online">在线</div>
                                <i class="fas fa-video" style="font-size: 48px; opacity: 0.5;"></i>
                            </div>
                            <div class="camera-info">
                                <div class="camera-name">食堂区域</div>
                                <div class="camera-location">
                                    <i class="fas fa-map-marker-alt"></i> 第一食堂入口
                                </div>
                                <div class="camera-controls">
                                    <button class="control-btn">
                                        <i class="fas fa-play"></i> 实时
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-history"></i> 回放
                                    </button>
                                    <button class="control-btn">
                                        <i class="fas fa-expand"></i> 全屏
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 门禁管理 -->
                <div class="tab-content" id="access-control">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">门禁通行记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="access-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>姓名</th>
                                        <th>身份</th>
                                        <th>门禁点</th>
                                        <th>通行方式</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-06-25 08:45</td>
                                        <td>张三</td>
                                        <td>学生</td>
                                        <td>教学楼A座大门</td>
                                        <td>人脸识别</td>
                                        <td><span class="access-status access-granted">通过</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 08:42</td>
                                        <td>李四</td>
                                        <td>教师</td>
                                        <td>办公楼B座</td>
                                        <td>校园卡</td>
                                        <td><span class="access-status access-granted">通过</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 08:38</td>
                                        <td>未知人员</td>
                                        <td>访客</td>
                                        <td>宿舍楼1号楼</td>
                                        <td>强行进入</td>
                                        <td><span class="access-status access-denied">拒绝</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 08:35</td>
                                        <td>王五</td>
                                        <td>学生</td>
                                        <td>图书馆</td>
                                        <td>二维码</td>
                                        <td><span class="access-status access-granted">通过</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 08:30</td>
                                        <td>赵六</td>
                                        <td>后勤</td>
                                        <td>实验楼C座</td>
                                        <td>指纹识别</td>
                                        <td><span class="access-status access-granted">通过</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 访客管理 -->
                <div class="tab-content" id="visitor-management">
                    <div class="visitor-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-user-plus"></i> 访客预约登记
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">访客姓名</label>
                                <input type="text" class="form-control" placeholder="请输入访客姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">身份证号</label>
                                <input type="text" class="form-control" placeholder="请输入身份证号">
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话</label>
                                <input type="tel" class="form-control" placeholder="请输入联系电话">
                            </div>
                            <div class="form-group">
                                <label class="form-label">来访目的</label>
                                <select class="form-control">
                                    <option>学术交流</option>
                                    <option>商务洽谈</option>
                                    <option>参观访问</option>
                                    <option>其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">被访人</label>
                                <input type="text" class="form-control" placeholder="请输入被访人姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">访问地点</label>
                                <select class="form-control">
                                    <option>教学楼A座</option>
                                    <option>办公楼B座</option>
                                    <option>实验楼C座</option>
                                    <option>图书馆</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">预约时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">预计停留时间</label>
                                <select class="form-control">
                                    <option>1小时内</option>
                                    <option>2小时内</option>
                                    <option>半天</option>
                                    <option>全天</option>
                                </select>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-check"></i> 提交申请
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">访客记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="visitor-table">
                                <thead>
                                    <tr>
                                        <th>访客姓名</th>
                                        <th>联系电话</th>
                                        <th>来访目的</th>
                                        <th>被访人</th>
                                        <th>预约时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>陈先生</td>
                                        <td>138****1234</td>
                                        <td>学术交流</td>
                                        <td>张教授</td>
                                        <td>2024-06-25 14:00</td>
                                        <td><span class="visitor-status status-approved">已批准</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>李女士</td>
                                        <td>139****5678</td>
                                        <td>商务洽谈</td>
                                        <td>王主任</td>
                                        <td>2024-06-26 09:00</td>
                                        <td><span class="visitor-status status-pending">待审批</span></td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">审批</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>刘先生</td>
                                        <td>137****9876</td>
                                        <td>参观访问</td>
                                        <td>赵老师</td>
                                        <td>2024-06-24 15:00</td>
                                        <td><span class="visitor-status status-expired">已过期</span></td>
                                        <td>
                                            <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">归档</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 巡逻记录 -->
                <div class="tab-content" id="patrol-records">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">今日巡逻记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="access-table">
                                <thead>
                                    <tr>
                                        <th>巡逻时间</th>
                                        <th>巡逻人员</th>
                                        <th>巡逻区域</th>
                                        <th>发现问题</th>
                                        <th>处理情况</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-06-25 06:00</td>
                                        <td>安保队员A</td>
                                        <td>教学楼区域</td>
                                        <td>无异常</td>
                                        <td>正常巡逻</td>
                                        <td><span class="access-status access-granted">正常</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 10:00</td>
                                        <td>安保队员B</td>
                                        <td>宿舍区域</td>
                                        <td>发现可疑人员</td>
                                        <td>已驱离并记录</td>
                                        <td><span class="access-status access-denied">异常</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 14:00</td>
                                        <td>安保队员C</td>
                                        <td>图书馆周边</td>
                                        <td>无异常</td>
                                        <td>正常巡逻</td>
                                        <td><span class="access-status access-granted">正常</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 18:00</td>
                                        <td>安保队员A</td>
                                        <td>体育馆区域</td>
                                        <td>设施损坏</td>
                                        <td>已上报维修</td>
                                        <td><span class="access-status access-denied">异常</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25 22:00</td>
                                        <td>安保队员D</td>
                                        <td>全校区域</td>
                                        <td>无异常</td>
                                        <td>夜间巡逻完成</td>
                                        <td><span class="access-status access-granted">正常</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('校园服务 > 安防监控系统');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 摄像头控制
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    const action = this.textContent.trim();
                    SmartCampus.showMessage(`正在${action}摄像头...`, 'info');
                }
            });
        });

        // 访客申请提交
        document.querySelector('.visitor-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('访客申请提交成功，等待审批', 'success');
            }, 1500);
        });

        // 模拟实时更新告警信息
        setInterval(() => {
            const alerts = document.querySelectorAll('.alert-time');
            alerts.forEach(alert => {
                const currentTime = alert.textContent;
                if (currentTime.includes('分钟前')) {
                    const minutes = parseInt(currentTime) + 1;
                    alert.textContent = `${minutes}分钟前`;
                }
            });
        }, 60000);
    </script>
</body>
</html>
