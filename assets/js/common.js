// 智慧校园管理系统 - 公共JavaScript

// 导航菜单配置
const menuConfig = [
    {
        title: '首页',
        icon: 'fas fa-home',
        url: 'index.html',
        id: 'home'
    },
    {
        title: '教学管理',
        icon: 'fas fa-chalkboard-teacher',
        id: 'teaching',
        children: [
            { title: '教学资源管理', url: 'teaching-resources.html', icon: 'fas fa-book' },
            { title: '教务综合管理', url: 'academic-affairs.html', icon: 'fas fa-clipboard-list' },
            { title: '教学评价系统', url: 'teaching-evaluation.html', icon: 'fas fa-star' }
        ]
    },
    {
        title: '学生管理',
        icon: 'fas fa-user-graduate',
        id: 'student',
        children: [
            { title: '学生信息中心', url: 'student-info.html', icon: 'fas fa-id-card' },
            { title: '学生考勤系统', url: 'student-attendance.html', icon: 'fas fa-clock' },
            { title: '心理健康咨询', url: 'psychological-health.html', icon: 'fas fa-heart' }
        ]
    },
    {
        title: '教师管理',
        icon: 'fas fa-users',
        id: 'teacher',
        children: [
            { title: '人事管理', url: 'hr-management.html', icon: 'fas fa-user-tie' },
            { title: '教师考勤排班', url: 'teacher-attendance.html', icon: 'fas fa-calendar-check' },
            { title: '教师发展档案', url: 'teacher-development.html', icon: 'fas fa-chart-line' }
        ]
    },
    {
        title: '校园服务',
        icon: 'fas fa-university',
        id: 'campus',
        children: [
            { title: '一卡通系统', url: 'card-system.html', icon: 'fas fa-credit-card' },
            { title: '安防监控系统', url: 'security-system.html', icon: 'fas fa-shield-alt' },
            { title: '后勤服务系统', url: 'logistics-system.html', icon: 'fas fa-tools' },
            { title: '宿舍管理系统', url: 'dormitory-system.html', icon: 'fas fa-bed' }
        ]
    },
    {
        title: '图书资源',
        icon: 'fas fa-book-open',
        url: 'library-system.html',
        id: 'library'
    },
    {
        title: '智慧课堂',
        icon: 'fas fa-laptop',
        url: 'smart-classroom.html',
        id: 'classroom'
    },
    {
        title: '信息发布',
        icon: 'fas fa-bullhorn',
        url: 'information-portal.html',
        id: 'portal'
    },
    {
        title: '数据分析',
        icon: 'fas fa-chart-bar',
        url: 'data-analytics.html',
        id: 'analytics'
    },
    {
        title: '系统设置',
        icon: 'fas fa-cog',
        url: 'system-settings.html',
        id: 'settings'
    }
];

// 生成侧边导航栏HTML
function generateSidebar() {
    let sidebarHTML = `
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-graduation-cap"></i> 智慧校园</h2>
                <p>管理系统</p>
            </div>
            <ul class="nav-menu">
    `;

    menuConfig.forEach(item => {
        if (item.children) {
            sidebarHTML += `
                <li class="nav-item">
                    <a href="#" class="nav-link nav-toggle" data-target="${item.id}">
                        <i class="${item.icon}"></i>
                        <span>${item.title}</span>
                        <i class="fas fa-chevron-down nav-arrow"></i>
                    </a>
                    <ul class="nav-submenu" id="${item.id}">
            `;
            item.children.forEach(child => {
                sidebarHTML += `
                    <li class="nav-item">
                        <a href="${child.url}" class="nav-link nav-sublink">
                            <i class="${child.icon}"></i>
                            <span>${child.title}</span>
                        </a>
                    </li>
                `;
            });
            sidebarHTML += `</ul></li>`;
        } else {
            sidebarHTML += `
                <li class="nav-item">
                    <a href="${item.url}" class="nav-link">
                        <i class="${item.icon}"></i>
                        <span>${item.title}</span>
                    </a>
                </li>
            `;
        }
    });

    sidebarHTML += `
            </ul>
        </div>
    `;

    return sidebarHTML;
}

// 生成顶部导航栏HTML
function generateTopNavbar(pageTitle = '智慧校园管理系统') {
    return `
        <div class="top-navbar">
            <div class="breadcrumb">
                <i class="fas fa-home"></i>
                <span>${pageTitle}</span>
            </div>
            <div class="user-info">
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <span>管理员</span>
            </div>
        </div>
    `;
}

// 初始化页面
function initializePage(pageTitle) {
    // 添加侧边导航栏
    const mainContainer = document.querySelector('.main-container');
    if (mainContainer) {
        mainContainer.insertAdjacentHTML('afterbegin', generateSidebar());
    }

    // 添加顶部导航栏
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.insertAdjacentHTML('afterbegin', generateTopNavbar(pageTitle));
    }

    // 设置当前页面导航高亮
    setActiveNavigation();

    // 绑定事件
    bindEvents();

    // 添加页面加载动画
    document.body.classList.add('fade-in');
}

// 设置当前页面导航高亮
function setActiveNavigation() {
    const currentPage = window.location.pathname.split('/').pop();
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href.includes(currentPage)) {
            link.classList.add('active');
            // 如果是子菜单，展开父菜单
            const parentSubmenu = link.closest('.nav-submenu');
            if (parentSubmenu) {
                parentSubmenu.style.display = 'block';
                const parentToggle = document.querySelector(`[data-target="${parentSubmenu.id}"]`);
                if (parentToggle) {
                    parentToggle.classList.add('active');
                }
            }
        }
    });
}

// 绑定事件
function bindEvents() {
    // 子菜单切换
    document.querySelectorAll('.nav-toggle').forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const submenu = document.getElementById(targetId);
            const arrow = this.querySelector('.nav-arrow');
            
            if (submenu.style.display === 'block') {
                submenu.style.display = 'none';
                arrow.style.transform = 'rotate(0deg)';
                this.classList.remove('active');
            } else {
                // 关闭其他子菜单
                document.querySelectorAll('.nav-submenu').forEach(menu => {
                    menu.style.display = 'none';
                });
                document.querySelectorAll('.nav-toggle').forEach(t => {
                    t.classList.remove('active');
                    t.querySelector('.nav-arrow').style.transform = 'rotate(0deg)';
                });
                
                // 打开当前子菜单
                submenu.style.display = 'block';
                arrow.style.transform = 'rotate(180deg)';
                this.classList.add('active');
            }
        });
    });

    // 移动端菜单切换
    const menuToggle = document.querySelector('.menu-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }

    // 通知按钮点击
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            alert('您有3条新通知');
        });
    }
}

// 显示加载动画
function showLoading() {
    const loadingHTML = `
        <div class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>加载中...</p>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);
}

// 隐藏加载动画
function hideLoading() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageHTML = `
        <div class="message-toast message-${type}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', messageHTML);
    
    const toast = document.querySelector('.message-toast:last-child');
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// 格式化数字
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 格式化日期
function formatDate(date) {
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// 导出公共函数
window.SmartCampus = {
    initializePage,
    showLoading,
    hideLoading,
    showMessage,
    formatNumber,
    formatDate
};
