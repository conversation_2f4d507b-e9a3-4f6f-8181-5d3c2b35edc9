<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学评价系统 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .evaluation-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .evaluation-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .course-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
        }

        .course-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .course-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            opacity: 0.9;
        }

        .question-group {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .question-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .rating-options {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .rating-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .rating-option:hover {
            background: rgba(52,152,219,0.1);
        }

        .rating-option input[type="radio"] {
            margin: 0;
        }

        .star-rating {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }

        .star {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .star.active,
        .star:hover {
            color: #f39c12;
        }

        .comment-box {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
        }

        .teacher-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .teacher-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .teacher-info {
            flex: 1;
        }

        .teacher-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .teacher-details {
            color: #666;
            margin-bottom: 10px;
        }

        .teacher-rating {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .rating-score {
            font-size: 18px;
            font-weight: bold;
            color: #f39c12;
        }

        .rating-stars {
            display: flex;
            gap: 2px;
        }

        .evaluation-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .report-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .report-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .report-date {
            color: #666;
            font-size: 14px;
        }

        .evaluation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .evaluation-item:last-child {
            border-bottom: none;
        }

        .evaluation-question {
            flex: 1;
            font-weight: 500;
            color: #2c3e50;
        }

        .evaluation-score {
            font-size: 16px;
            font-weight: bold;
            padding: 5px 12px;
            border-radius: 20px;
            color: white;
        }

        .score-excellent {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }

        .score-good {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }

        .score-average {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .score-poor {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-star"></i> 教学评价系统
                </h1>

                <!-- 功能选项卡 -->
                <div class="evaluation-tabs">
                    <button class="tab-btn active" data-tab="student-evaluation">
                        <i class="fas fa-user-graduate"></i> 学生评教
                    </button>
                    <button class="tab-btn" data-tab="peer-evaluation">
                        <i class="fas fa-users"></i> 教师互评
                    </button>
                    <button class="tab-btn" data-tab="evaluation-reports">
                        <i class="fas fa-chart-bar"></i> 评教报告
                    </button>
                </div>

                <!-- 学生评教 -->
                <div class="tab-content active" id="student-evaluation">
                    <div class="evaluation-form">
                        <div class="course-info">
                            <div class="course-title">高等数学 - 期末教学评价</div>
                            <div class="course-details">
                                <div><i class="fas fa-user-tie"></i> 任课教师：张教授</div>
                                <div><i class="fas fa-calendar"></i> 学期：2024春季学期</div>
                                <div><i class="fas fa-users"></i> 班级：计科1班</div>
                                <div><i class="fas fa-clock"></i> 课时：64学时</div>
                            </div>
                        </div>

                        <div class="question-group">
                            <div class="question-title">1. 教师教学态度认真，备课充分</div>
                            <div class="star-rating" data-question="1">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                            <div class="rating-options">
                                <label class="rating-option">
                                    <input type="radio" name="q1" value="5">
                                    <span>非常同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q1" value="4">
                                    <span>同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q1" value="3">
                                    <span>一般</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q1" value="2">
                                    <span>不同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q1" value="1">
                                    <span>非常不同意</span>
                                </label>
                            </div>
                        </div>

                        <div class="question-group">
                            <div class="question-title">2. 教学内容丰富，重点突出，条理清晰</div>
                            <div class="star-rating" data-question="2">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                            <div class="rating-options">
                                <label class="rating-option">
                                    <input type="radio" name="q2" value="5">
                                    <span>非常同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q2" value="4">
                                    <span>同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q2" value="3">
                                    <span>一般</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q2" value="2">
                                    <span>不同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q2" value="1">
                                    <span>非常不同意</span>
                                </label>
                            </div>
                        </div>

                        <div class="question-group">
                            <div class="question-title">3. 教学方法得当，能够激发学习兴趣</div>
                            <div class="star-rating" data-question="3">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                            <div class="rating-options">
                                <label class="rating-option">
                                    <input type="radio" name="q3" value="5">
                                    <span>非常同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q3" value="4">
                                    <span>同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q3" value="3">
                                    <span>一般</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q3" value="2">
                                    <span>不同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q3" value="1">
                                    <span>非常不同意</span>
                                </label>
                            </div>
                        </div>

                        <div class="question-group">
                            <div class="question-title">4. 课堂互动良好，能够及时答疑解惑</div>
                            <div class="star-rating" data-question="4">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                            <div class="rating-options">
                                <label class="rating-option">
                                    <input type="radio" name="q4" value="5">
                                    <span>非常同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q4" value="4">
                                    <span>同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q4" value="3">
                                    <span>一般</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q4" value="2">
                                    <span>不同意</span>
                                </label>
                                <label class="rating-option">
                                    <input type="radio" name="q4" value="1">
                                    <span>非常不同意</span>
                                </label>
                            </div>
                        </div>

                        <div class="question-group">
                            <div class="question-title">5. 您对本课程的总体评价</div>
                            <div class="star-rating" data-question="overall">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                        </div>

                        <div class="question-group">
                            <div class="question-title">6. 意见和建议（选填）</div>
                            <textarea class="comment-box" placeholder="请写下您对本课程的意见和建议..."></textarea>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px; font-size: 16px;">
                                <i class="fas fa-paper-plane"></i> 提交评价
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 教师互评 -->
                <div class="tab-content" id="peer-evaluation">
                    <div class="teacher-card">
                        <div class="teacher-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="teacher-info">
                            <div class="teacher-name">张教授</div>
                            <div class="teacher-details">
                                数学系 | 教授 | 高等数学、线性代数
                            </div>
                            <div class="teacher-rating">
                                <span class="rating-score">4.8</span>
                                <div class="rating-stars">
                                    <span style="color: #f39c12;">★★★★★</span>
                                </div>
                                <span style="color: #666;">(128条评价)</span>
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-primary">
                                <i class="fas fa-star"></i> 评价
                            </button>
                        </div>
                    </div>

                    <div class="teacher-card">
                        <div class="teacher-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="teacher-info">
                            <div class="teacher-name">李老师</div>
                            <div class="teacher-details">
                                外语系 | 副教授 | 大学英语、英语听力
                            </div>
                            <div class="teacher-rating">
                                <span class="rating-score">4.6</span>
                                <div class="rating-stars">
                                    <span style="color: #f39c12;">★★★★☆</span>
                                </div>
                                <span style="color: #666;">(95条评价)</span>
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-primary">
                                <i class="fas fa-star"></i> 评价
                            </button>
                        </div>
                    </div>

                    <div class="teacher-card">
                        <div class="teacher-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="teacher-info">
                            <div class="teacher-name">王老师</div>
                            <div class="teacher-details">
                                计算机系 | 讲师 | 数据结构、算法设计
                            </div>
                            <div class="teacher-rating">
                                <span class="rating-score">4.9</span>
                                <div class="rating-stars">
                                    <span style="color: #f39c12;">★★★★★</span>
                                </div>
                                <span style="color: #666;">(156条评价)</span>
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-primary">
                                <i class="fas fa-star"></i> 评价
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 评教报告 -->
                <div class="tab-content" id="evaluation-reports">
                    <div class="evaluation-stats">
                        <div class="stat-card">
                            <div class="stat-number" style="color: #2ecc71;">4.7</div>
                            <div class="stat-label">平均评分</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" style="color: #3498db;">1,248</div>
                            <div class="stat-label">评价总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" style="color: #f39c12;">92.5%</div>
                            <div class="stat-label">参与率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" style="color: #e74c3c;">85.6%</div>
                            <div class="stat-label">满意度</div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <div class="report-title">高等数学 - 张教授 - 评教报告</div>
                            <div class="report-date">2024年6月</div>
                        </div>
                        
                        <div class="evaluation-item">
                            <div class="evaluation-question">教学态度认真，备课充分</div>
                            <div class="evaluation-score score-excellent">4.8</div>
                        </div>
                        
                        <div class="evaluation-item">
                            <div class="evaluation-question">教学内容丰富，重点突出</div>
                            <div class="evaluation-score score-excellent">4.7</div>
                        </div>
                        
                        <div class="evaluation-item">
                            <div class="evaluation-question">教学方法得当，激发兴趣</div>
                            <div class="evaluation-score score-good">4.5</div>
                        </div>
                        
                        <div class="evaluation-item">
                            <div class="evaluation-question">课堂互动良好，及时答疑</div>
                            <div class="evaluation-score score-good">4.6</div>
                        </div>
                        
                        <div class="evaluation-item">
                            <div class="evaluation-question">总体评价</div>
                            <div class="evaluation-score score-excellent">4.7</div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-chart-pie"></i> 评价分布统计
                        </h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie" style="font-size: 48px; opacity: 0.3;"></i>
                            <span style="margin-left: 15px;">评价分布图表加载中...</span>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-chart-line"></i> 评分趋势分析
                        </h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line" style="font-size: 48px; opacity: 0.3;"></i>
                            <span style="margin-left: 15px;">趋势分析图表加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('教学管理 > 教学评价系统');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 星级评分交互
        document.querySelectorAll('.star-rating').forEach(rating => {
            const stars = rating.querySelectorAll('.star');
            
            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    const ratingValue = index + 1;
                    
                    // 更新星星显示
                    stars.forEach((s, i) => {
                        if (i < ratingValue) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                    
                    // 同步单选按钮
                    const questionNum = rating.dataset.question;
                    if (questionNum !== 'overall') {
                        const radio = document.querySelector(`input[name="q${questionNum}"][value="${ratingValue}"]`);
                        if (radio) radio.checked = true;
                    }
                });
                
                star.addEventListener('mouseenter', function() {
                    stars.forEach((s, i) => {
                        if (i <= index) {
                            s.style.color = '#f39c12';
                        } else {
                            s.style.color = '#ddd';
                        }
                    });
                });
            });
            
            rating.addEventListener('mouseleave', function() {
                stars.forEach(s => {
                    if (!s.classList.contains('active')) {
                        s.style.color = '#ddd';
                    }
                });
            });
        });

        // 提交评价
        document.querySelector('.btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('评价提交成功！感谢您的参与。', 'success');
            }, 1500);
        });
    </script>
</body>
</html>
