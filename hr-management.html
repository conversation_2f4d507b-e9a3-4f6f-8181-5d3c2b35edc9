<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人事管理 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .hr-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .hr-stat {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .hr-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .hr-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #667eea;
            background: linear-gradient(135deg, rgba(102,126,234,0.1), rgba(118,75,162,0.1));
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .teacher-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .teacher-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .teacher-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .teacher-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .teacher-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .teacher-info {
            flex: 1;
        }

        .teacher-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .teacher-title {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .teacher-dept {
            color: #3498db;
            font-size: 12px;
        }

        .teacher-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .detail-label {
            color: #666;
        }

        .detail-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .teacher-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-align: center;
        }

        .action-btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
        }

        .appointment-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .appointment-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .appointment-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .appointment-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #cce5ff;
            color: #004085;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .title-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .level-professor {
            background: #ffd700;
            color: #8b6914;
        }

        .level-associate {
            background: #c0c0c0;
            color: #555;
        }

        .level-lecturer {
            background: #cd7f32;
            color: #fff;
        }

        .level-assistant {
            background: #e0e0e0;
            color: #666;
        }

        .certificate-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .certificate-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .cert-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .cert-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .cert-status {
            font-size: 12px;
            font-weight: 500;
        }

        .cert-valid {
            color: #2ecc71;
        }

        .cert-expired {
            color: #e74c3c;
        }

        .training-timeline {
            position: relative;
            padding-left: 30px;
        }

        .training-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }

        .training-item {
            position: relative;
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .training-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #667eea;
        }

        .training-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .training-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .training-desc {
            font-size: 14px;
            color: #666;
        }

        .search-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
        }

        .filter-select {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            min-width: 120px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-user-tie"></i> 人事管理
                </h1>

                <!-- 人事统计 -->
                <div class="hr-dashboard">
                    <div class="hr-stat">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">在职教师</div>
                    </div>
                    <div class="hr-stat">
                        <div class="stat-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">教授</div>
                    </div>
                    <div class="hr-stat">
                        <div class="stat-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">本月新入职</div>
                    </div>
                    <div class="hr-stat">
                        <div class="stat-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">待审批职称</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="hr-tabs">
                    <button class="tab-btn active" data-tab="teacher-info">
                        <i class="fas fa-id-card"></i> 教师信息
                    </button>
                    <button class="tab-btn" data-tab="appointment">
                        <i class="fas fa-handshake"></i> 聘任管理
                    </button>
                    <button class="tab-btn" data-tab="title-evaluation">
                        <i class="fas fa-award"></i> 职称评定
                    </button>
                    <button class="tab-btn" data-tab="certificates">
                        <i class="fas fa-certificate"></i> 证书管理
                    </button>
                    <button class="tab-btn" data-tab="training">
                        <i class="fas fa-chalkboard-teacher"></i> 培训记录
                    </button>
                </div>

                <!-- 教师信息 -->
                <div class="tab-content active" id="teacher-info">
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="搜索教师姓名、工号或专业...">
                        <select class="filter-select">
                            <option>全部院系</option>
                            <option>计算机学院</option>
                            <option>数学学院</option>
                            <option>外语学院</option>
                            <option>机械学院</option>
                        </select>
                        <select class="filter-select">
                            <option>全部职称</option>
                            <option>教授</option>
                            <option>副教授</option>
                            <option>讲师</option>
                            <option>助教</option>
                        </select>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-plus"></i> 新增教师
                        </button>
                    </div>

                    <div class="teacher-grid">
                        <div class="teacher-card">
                            <div class="teacher-header">
                                <div class="teacher-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="teacher-info">
                                    <div class="teacher-name">张教授</div>
                                    <div class="teacher-title">
                                        <span class="title-level level-professor">教授</span>
                                        博士生导师
                                    </div>
                                    <div class="teacher-dept">计算机科学与技术学院</div>
                                </div>
                            </div>
                            <div class="teacher-details">
                                <div class="detail-item">
                                    <span class="detail-label">工号</span>
                                    <span class="detail-value">T2024001</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">入职时间</span>
                                    <span class="detail-value">2015-09-01</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">专业方向</span>
                                    <span class="detail-value">人工智能</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">学历</span>
                                    <span class="detail-value">博士</span>
                                </div>
                            </div>
                            <div class="teacher-actions">
                                <button class="action-btn">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                <button class="action-btn">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn">
                                    <i class="fas fa-file-alt"></i> 档案
                                </button>
                            </div>
                        </div>

                        <div class="teacher-card">
                            <div class="teacher-header">
                                <div class="teacher-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="teacher-info">
                                    <div class="teacher-name">李副教授</div>
                                    <div class="teacher-title">
                                        <span class="title-level level-associate">副教授</span>
                                        硕士生导师
                                    </div>
                                    <div class="teacher-dept">数学与统计学院</div>
                                </div>
                            </div>
                            <div class="teacher-details">
                                <div class="detail-item">
                                    <span class="detail-label">工号</span>
                                    <span class="detail-value">T2024002</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">入职时间</span>
                                    <span class="detail-value">2018-03-15</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">专业方向</span>
                                    <span class="detail-value">应用数学</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">学历</span>
                                    <span class="detail-value">博士</span>
                                </div>
                            </div>
                            <div class="teacher-actions">
                                <button class="action-btn">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                <button class="action-btn">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn">
                                    <i class="fas fa-file-alt"></i> 档案
                                </button>
                            </div>
                        </div>

                        <div class="teacher-card">
                            <div class="teacher-header">
                                <div class="teacher-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="teacher-info">
                                    <div class="teacher-name">王讲师</div>
                                    <div class="teacher-title">
                                        <span class="title-level level-lecturer">讲师</span>
                                        专任教师
                                    </div>
                                    <div class="teacher-dept">外国语学院</div>
                                </div>
                            </div>
                            <div class="teacher-details">
                                <div class="detail-item">
                                    <span class="detail-label">工号</span>
                                    <span class="detail-value">T2024003</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">入职时间</span>
                                    <span class="detail-value">2020-09-01</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">专业方向</span>
                                    <span class="detail-value">英语语言文学</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">学历</span>
                                    <span class="detail-value">硕士</span>
                                </div>
                            </div>
                            <div class="teacher-actions">
                                <button class="action-btn">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                <button class="action-btn">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="action-btn">
                                    <i class="fas fa-file-alt"></i> 档案
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 聘任管理 -->
                <div class="tab-content" id="appointment">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">聘任申请管理</h3>
                        </div>
                        <div class="card-body">
                            <table class="appointment-table">
                                <thead>
                                    <tr>
                                        <th>申请人</th>
                                        <th>申请职位</th>
                                        <th>所属院系</th>
                                        <th>申请时间</th>
                                        <th>审批状态</th>
                                        <th>审批人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>陈博士</td>
                                        <td>副教授</td>
                                        <td>计算机学院</td>
                                        <td>2024-06-20</td>
                                        <td><span class="status-badge status-pending">待审批</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">审批</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>刘硕士</td>
                                        <td>讲师</td>
                                        <td>数学学院</td>
                                        <td>2024-06-18</td>
                                        <td><span class="status-badge status-completed">已通过</span></td>
                                        <td>院长</td>
                                        <td>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>赵老师</td>
                                        <td>教授</td>
                                        <td>外语学院</td>
                                        <td>2024-06-15</td>
                                        <td><span class="status-badge status-active">审批中</span></td>
                                        <td>校长</td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">跟进</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 职称评定 -->
                <div class="tab-content" id="title-evaluation">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">职称评定申请</h3>
                        </div>
                        <div class="card-body">
                            <table class="appointment-table">
                                <thead>
                                    <tr>
                                        <th>申请人</th>
                                        <th>当前职称</th>
                                        <th>申请职称</th>
                                        <th>申请时间</th>
                                        <th>评审状态</th>
                                        <th>评审结果</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>孙讲师</td>
                                        <td>讲师</td>
                                        <td>副教授</td>
                                        <td>2024-05-15</td>
                                        <td><span class="status-badge status-active">评审中</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">材料</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>周副教授</td>
                                        <td>副教授</td>
                                        <td>教授</td>
                                        <td>2024-04-20</td>
                                        <td><span class="status-badge status-completed">已完成</span></td>
                                        <td><span class="status-badge status-completed">通过</span></td>
                                        <td>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>吴助教</td>
                                        <td>助教</td>
                                        <td>讲师</td>
                                        <td>2024-03-10</td>
                                        <td><span class="status-badge status-completed">已完成</span></td>
                                        <td><span class="status-badge status-rejected">未通过</span></td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">反馈</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 证书管理 -->
                <div class="tab-content" id="certificates">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">教师资格证书管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="certificate-grid">
                                <div class="certificate-card">
                                    <div class="cert-title">高等学校教师资格证</div>
                                    <div class="cert-info">持有人：张教授</div>
                                    <div class="cert-info">证书编号：20150912345678</div>
                                    <div class="cert-info">颁发日期：2015-09-01</div>
                                    <div class="cert-info">有效期：长期有效</div>
                                    <div class="cert-status cert-valid">✓ 有效</div>
                                </div>

                                <div class="certificate-card">
                                    <div class="cert-title">专业技术职务任职资格证书</div>
                                    <div class="cert-info">持有人：李副教授</div>
                                    <div class="cert-info">证书编号：20180315123456</div>
                                    <div class="cert-info">颁发日期：2018-03-15</div>
                                    <div class="cert-info">职务：副教授</div>
                                    <div class="cert-status cert-valid">✓ 有效</div>
                                </div>

                                <div class="certificate-card">
                                    <div class="cert-title">普通话水平测试等级证书</div>
                                    <div class="cert-info">持有人：王讲师</div>
                                    <div class="cert-info">证书编号：20200901987654</div>
                                    <div class="cert-info">颁发日期：2020-09-01</div>
                                    <div class="cert-info">等级：二级甲等</div>
                                    <div class="cert-status cert-valid">✓ 有效</div>
                                </div>

                                <div class="certificate-card">
                                    <div class="cert-title">继续教育证书</div>
                                    <div class="cert-info">持有人：陈讲师</div>
                                    <div class="cert-info">证书编号：20230601111111</div>
                                    <div class="cert-info">颁发日期：2023-06-01</div>
                                    <div class="cert-info">有效期至：2024-06-01</div>
                                    <div class="cert-status cert-expired">⚠ 即将过期</div>
                                </div>

                                <div class="certificate-card">
                                    <div class="cert-title">学位证书</div>
                                    <div class="cert-info">持有人：刘博士</div>
                                    <div class="cert-info">证书编号：20120701222222</div>
                                    <div class="cert-info">颁发日期：2012-07-01</div>
                                    <div class="cert-info">学位：工学博士</div>
                                    <div class="cert-status cert-valid">✓ 有效</div>
                                </div>

                                <div class="certificate-card">
                                    <div class="cert-title">外语水平证书</div>
                                    <div class="cert-info">持有人：赵老师</div>
                                    <div class="cert-info">证书编号：20190315333333</div>
                                    <div class="cert-info">颁发日期：2019-03-15</div>
                                    <div class="cert-info">等级：英语六级</div>
                                    <div class="cert-status cert-valid">✓ 有效</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 培训记录 -->
                <div class="tab-content" id="training">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">教师培训记录</h3>
                        </div>
                        <div class="card-body">
                            <div class="training-timeline">
                                <div class="training-item">
                                    <div class="training-date">2024年6月15日</div>
                                    <div class="training-title">智慧教学技术培训</div>
                                    <div class="training-desc">学习最新的智慧教学技术和教学方法，提升教学质量和效率</div>
                                </div>

                                <div class="training-item">
                                    <div class="training-date">2024年5月20日</div>
                                    <div class="training-title">师德师风建设专题讲座</div>
                                    <div class="training-desc">加强师德师风建设，提高教师职业道德水平</div>
                                </div>

                                <div class="training-item">
                                    <div class="training-date">2024年4月10日</div>
                                    <div class="training-title">教学科研能力提升研修班</div>
                                    <div class="training-desc">提升教师教学科研能力，促进教师专业发展</div>
                                </div>

                                <div class="training-item">
                                    <div class="training-date">2024年3月25日</div>
                                    <div class="training-title">信息化教学能力培训</div>
                                    <div class="training-desc">掌握现代信息技术在教学中的应用，提高信息化教学水平</div>
                                </div>

                                <div class="training-item">
                                    <div class="training-date">2024年2月28日</div>
                                    <div class="training-title">新教师入职培训</div>
                                    <div class="training-desc">帮助新入职教师快速适应工作环境，了解学校规章制度</div>
                                </div>

                                <div class="training-item">
                                    <div class="training-date">2024年1月15日</div>
                                    <div class="training-title">教学质量评估与改进研讨会</div>
                                    <div class="training-desc">探讨教学质量评估方法，制定教学改进措施</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('教师管理 > 人事管理');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 教师卡片操作
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`正在${action}教师信息...`, 'info');
            });
        });

        // 搜索功能
        document.querySelector('.search-bar .btn-primary').addEventListener('click', function() {
            SmartCampus.showMessage('正在搜索教师信息...', 'info');
        });

        // 新增教师
        document.querySelector('.search-bar .btn-success').addEventListener('click', function() {
            SmartCampus.showMessage('打开新增教师表单...', 'info');
        });

        // 审批操作
        document.querySelectorAll('table .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`正在执行${action}操作...`, 'info');
            });
        });
    </script>
</body>
</html>
