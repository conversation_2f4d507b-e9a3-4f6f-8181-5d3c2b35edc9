<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一卡通系统 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .card-header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .card-visual {
            width: 200px;
            height: 120px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            position: relative;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .card-visual::before {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 20px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }

        .card-info {
            flex: 1;
        }

        .card-balance {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .card-number {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
        }

        .card-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .service-icon {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .service-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .service-desc {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .service-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .transaction-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .transaction-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .transaction-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .transaction-table tr:hover {
            background: #f8f9fa;
        }

        .amount-positive {
            color: #2ecc71;
            font-weight: bold;
        }

        .amount-negative {
            color: #e74c3c;
            font-weight: bold;
        }

        .recharge-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .amount-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .amount-option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .amount-option:hover {
            border-color: #3498db;
            background: rgba(52,152,219,0.1);
        }

        .amount-option.selected {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }

        .payment-methods {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }

        .payment-method {
            flex: 1;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-method:hover {
            border-color: #3498db;
        }

        .payment-method.selected {
            border-color: #3498db;
            background: rgba(52,152,219,0.1);
        }

        .payment-icon {
            font-size: 24px;
            margin-bottom: 8px;
            color: #3498db;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-credit-card"></i> 一卡通系统
                </h1>

                <!-- 校园卡信息 -->
                <div class="card-header-section">
                    <div class="card-visual">
                        <div>
                            <div style="font-size: 14px; margin-bottom: 5px;">智慧校园卡</div>
                            <div style="font-size: 12px; opacity: 0.8;">SMART CAMPUS CARD</div>
                        </div>
                    </div>
                    <div class="card-info">
                        <div class="card-balance">¥ 268.50</div>
                        <div class="card-number">卡号：2024001001</div>
                        <div class="card-actions">
                            <button class="action-btn" onclick="showRecharge()">
                                <i class="fas fa-plus"></i> 充值
                            </button>
                            <button class="action-btn" onclick="showTransactions()">
                                <i class="fas fa-list"></i> 交易记录
                            </button>
                            <button class="action-btn" onclick="reportLoss()">
                                <i class="fas fa-ban"></i> 挂失
                            </button>
                            <button class="action-btn" onclick="downloadStatement()">
                                <i class="fas fa-download"></i> 对账单
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 消费统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">¥ 45.80</div>
                        <div class="stat-label">今日消费</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥ 312.60</div>
                        <div class="stat-label">本月消费</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥ 1,256.40</div>
                        <div class="stat-label">学期消费</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">消费次数</div>
                    </div>
                </div>

                <!-- 校园服务 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-store"></i> 校园服务
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="service-grid">
                            <div class="service-card" onclick="useService('canteen')">
                                <div class="service-icon">
                                    <i class="fas fa-utensils"></i>
                                </div>
                                <div class="service-title">食堂消费</div>
                                <div class="service-desc">刷卡或扫码支付餐费</div>
                                <div class="service-status status-online">服务正常</div>
                            </div>

                            <div class="service-card" onclick="useService('supermarket')">
                                <div class="service-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="service-title">校园超市</div>
                                <div class="service-desc">购买日用品和学习用品</div>
                                <div class="service-status status-online">服务正常</div>
                            </div>

                            <div class="service-card" onclick="useService('library')">
                                <div class="service-icon">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="service-title">图书馆</div>
                                <div class="service-desc">借阅图书和使用设施</div>
                                <div class="service-status status-online">服务正常</div>
                            </div>

                            <div class="service-card" onclick="useService('laundry')">
                                <div class="service-icon">
                                    <i class="fas fa-tshirt"></i>
                                </div>
                                <div class="service-title">洗衣服务</div>
                                <div class="service-desc">宿舍洗衣机使用</div>
                                <div class="service-status status-online">服务正常</div>
                            </div>

                            <div class="service-card" onclick="useService('print')">
                                <div class="service-icon">
                                    <i class="fas fa-print"></i>
                                </div>
                                <div class="service-title">打印复印</div>
                                <div class="service-desc">文档打印和复印服务</div>
                                <div class="service-status status-online">服务正常</div>
                            </div>

                            <div class="service-card" onclick="useService('access')">
                                <div class="service-icon">
                                    <i class="fas fa-door-open"></i>
                                </div>
                                <div class="service-title">门禁通行</div>
                                <div class="service-desc">宿舍和教学楼门禁</div>
                                <div class="service-status status-online">服务正常</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 充值表单 -->
                <div class="recharge-form" id="recharge-form" style="display: none;">
                    <h3 style="margin-bottom: 20px;">
                        <i class="fas fa-plus-circle"></i> 校园卡充值
                    </h3>
                    <div class="form-group">
                        <label class="form-label">选择充值金额</label>
                        <div class="amount-options">
                            <div class="amount-option" data-amount="50">¥50</div>
                            <div class="amount-option" data-amount="100">¥100</div>
                            <div class="amount-option" data-amount="200">¥200</div>
                            <div class="amount-option" data-amount="300">¥300</div>
                            <div class="amount-option" data-amount="500">¥500</div>
                            <div class="amount-option" data-amount="custom">自定义</div>
                        </div>
                    </div>
                    <div class="form-group" id="custom-amount" style="display: none;">
                        <label class="form-label">自定义金额</label>
                        <input type="number" class="form-control" placeholder="请输入充值金额" min="10" max="1000">
                    </div>
                    <div class="form-group">
                        <label class="form-label">支付方式</label>
                        <div class="payment-methods">
                            <div class="payment-method" data-method="wechat">
                                <div class="payment-icon">
                                    <i class="fab fa-weixin" style="color: #2ecc71;"></i>
                                </div>
                                <div>微信支付</div>
                            </div>
                            <div class="payment-method" data-method="alipay">
                                <div class="payment-icon">
                                    <i class="fab fa-alipay" style="color: #1890ff;"></i>
                                </div>
                                <div>支付宝</div>
                            </div>
                            <div class="payment-method" data-method="bank">
                                <div class="payment-icon">
                                    <i class="fas fa-credit-card" style="color: #f39c12;"></i>
                                </div>
                                <div>银行卡</div>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 30px;">
                        <button class="btn btn-success" style="padding: 12px 40px;">
                            <i class="fas fa-credit-card"></i> 立即充值
                        </button>
                        <button class="btn btn-secondary" style="padding: 12px 40px; margin-left: 10px;" onclick="hideRecharge()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </div>

                <!-- 交易记录 -->
                <div class="card" id="transactions-section" style="display: none;">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> 最近交易记录
                        </h3>
                    </div>
                    <div class="card-body">
                        <table class="transaction-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>商户</th>
                                    <th>类型</th>
                                    <th>金额</th>
                                    <th>余额</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-06-25 12:30</td>
                                    <td>第一食堂</td>
                                    <td>餐费</td>
                                    <td class="amount-negative">-¥15.50</td>
                                    <td>¥268.50</td>
                                    <td><span class="status-badge status-online">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2024-06-25 08:45</td>
                                    <td>校园超市</td>
                                    <td>购物</td>
                                    <td class="amount-negative">-¥8.80</td>
                                    <td>¥284.00</td>
                                    <td><span class="status-badge status-online">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2024-06-24 20:15</td>
                                    <td>微信充值</td>
                                    <td>充值</td>
                                    <td class="amount-positive">+¥200.00</td>
                                    <td>¥292.80</td>
                                    <td><span class="status-badge status-online">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2024-06-24 18:20</td>
                                    <td>第二食堂</td>
                                    <td>餐费</td>
                                    <td class="amount-negative">-¥12.30</td>
                                    <td>¥92.80</td>
                                    <td><span class="status-badge status-online">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2024-06-24 14:10</td>
                                    <td>图书馆</td>
                                    <td>打印</td>
                                    <td class="amount-negative">-¥3.50</td>
                                    <td>¥105.10</td>
                                    <td><span class="status-badge status-online">成功</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('校园服务 > 一卡通系统');

        // 显示充值表单
        function showRecharge() {
            document.getElementById('recharge-form').style.display = 'block';
            document.getElementById('recharge-form').scrollIntoView({ behavior: 'smooth' });
        }

        // 隐藏充值表单
        function hideRecharge() {
            document.getElementById('recharge-form').style.display = 'none';
        }

        // 显示交易记录
        function showTransactions() {
            document.getElementById('transactions-section').style.display = 'block';
            document.getElementById('transactions-section').scrollIntoView({ behavior: 'smooth' });
        }

        // 挂失
        function reportLoss() {
            if (confirm('确定要挂失校园卡吗？挂失后需要到服务中心解挂。')) {
                SmartCampus.showMessage('挂失申请已提交，请尽快到服务中心办理', 'success');
            }
        }

        // 下载对账单
        function downloadStatement() {
            SmartCampus.showMessage('正在生成对账单，请稍候...', 'info');
            setTimeout(() => {
                SmartCampus.showMessage('对账单生成完成，开始下载', 'success');
            }, 2000);
        }

        // 使用服务
        function useService(service) {
            const serviceNames = {
                'canteen': '食堂消费',
                'supermarket': '校园超市',
                'library': '图书馆',
                'laundry': '洗衣服务',
                'print': '打印复印',
                'access': '门禁通行'
            };
            SmartCampus.showMessage(`正在使用${serviceNames[service]}服务...`, 'info');
        }

        // 金额选择
        document.querySelectorAll('.amount-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.amount-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
                
                if (this.dataset.amount === 'custom') {
                    document.getElementById('custom-amount').style.display = 'block';
                } else {
                    document.getElementById('custom-amount').style.display = 'none';
                }
            });
        });

        // 支付方式选择
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 充值确认
        document.querySelector('#recharge-form .btn-success').addEventListener('click', function() {
            const selectedAmount = document.querySelector('.amount-option.selected');
            const selectedPayment = document.querySelector('.payment-method.selected');
            
            if (!selectedAmount || !selectedPayment) {
                SmartCampus.showMessage('请选择充值金额和支付方式', 'info');
                return;
            }
            
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('充值成功！余额已更新', 'success');
                hideRecharge();
            }, 2000);
        });
    </script>
</body>
</html>
