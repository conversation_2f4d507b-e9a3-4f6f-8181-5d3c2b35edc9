<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师考勤排班 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .attendance-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .attendance-stat {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .attendance-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .attendance-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #667eea;
            background: linear-gradient(135deg, rgba(102,126,234,0.1), rgba(118,75,162,0.1));
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .schedule-grid {
            display: grid;
            grid-template-columns: 100px repeat(7, 1fr);
            gap: 1px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .schedule-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 500;
            font-size: 14px;
        }

        .schedule-time {
            background: #f8f9fa;
            padding: 15px 10px;
            text-align: center;
            font-weight: 500;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .schedule-cell {
            background: white;
            padding: 10px;
            min-height: 80px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .schedule-cell:hover {
            background: #f8f9fa;
        }

        .schedule-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px;
            border-radius: 6px;
            font-size: 11px;
            margin-bottom: 5px;
            line-height: 1.2;
        }

        .schedule-item.leave {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .schedule-item.training {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .attendance-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .attendance-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .attendance-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-present {
            background: #d4edda;
            color: #155724;
        }

        .status-late {
            background: #fff3cd;
            color: #856404;
        }

        .status-absent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-leave {
            background: #cce5ff;
            color: #004085;
        }

        .leave-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .performance-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .performance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .performance-score {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
        }

        .checkin-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 30px rgba(102,126,234,0.3);
        }

        .checkin-time {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .checkin-date {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .checkin-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .checkin-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        .holiday-calendar {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-nav {
            display: flex;
            gap: 10px;
        }

        .calendar-nav button {
            background: none;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-nav button:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .calendar-day {
            background: white;
            padding: 15px 10px;
            text-align: center;
            min-height: 60px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-day:hover {
            background: #f8f9fa;
        }

        .calendar-day.today {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .calendar-day.holiday {
            background: #ffe6e6;
            color: #e74c3c;
        }

        .calendar-day.workday {
            background: #e6f3ff;
            color: #3498db;
        }

        .day-number {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .day-status {
            font-size: 10px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-user-clock"></i> 教师考勤排班
                </h1>

                <!-- 考勤统计 -->
                <div class="attendance-dashboard">
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-number">98.5%</div>
                        <div class="stat-label">本月出勤率</div>
                    </div>
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-number">152</div>
                        <div class="stat-label">今日在岗</div>
                    </div>
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-number">4</div>
                        <div class="stat-label">请假人数</div>
                    </div>
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">2,340</div>
                        <div class="stat-label">本月工时</div>
                    </div>
                </div>

                <!-- 签到面板 -->
                <div class="checkin-panel">
                    <div class="checkin-time" id="current-time">08:45:32</div>
                    <div class="checkin-date" id="current-date">2024年6月25日 星期二</div>
                    <div>
                        <button class="checkin-btn" onclick="teacherCheckin()">
                            <i class="fas fa-sign-in-alt"></i> 签到
                        </button>
                        <button class="checkin-btn" onclick="teacherCheckout()">
                            <i class="fas fa-sign-out-alt"></i> 签退
                        </button>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="attendance-tabs">
                    <button class="tab-btn active" data-tab="schedule">
                        <i class="fas fa-calendar-week"></i> 排班表
                    </button>
                    <button class="tab-btn" data-tab="attendance-record">
                        <i class="fas fa-list"></i> 考勤记录
                    </button>
                    <button class="tab-btn" data-tab="leave-management">
                        <i class="fas fa-file-alt"></i> 假勤管理
                    </button>
                    <button class="tab-btn" data-tab="performance">
                        <i class="fas fa-chart-line"></i> 绩效统计
                    </button>
                    <button class="tab-btn" data-tab="holiday-calendar">
                        <i class="fas fa-calendar"></i> 节假日
                    </button>
                </div>

                <!-- 排班表 -->
                <div class="tab-content active" id="schedule">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">本周教师排班表</h3>
                        </div>
                        <div class="card-body">
                            <div class="schedule-grid">
                                <div class="schedule-header">时间</div>
                                <div class="schedule-header">周一</div>
                                <div class="schedule-header">周二</div>
                                <div class="schedule-header">周三</div>
                                <div class="schedule-header">周四</div>
                                <div class="schedule-header">周五</div>
                                <div class="schedule-header">周六</div>
                                <div class="schedule-header">周日</div>

                                <div class="schedule-time">08:00<br>09:40</div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">张教授<br>高等数学</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">李老师<br>英语听力</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">王老师<br>计算机基础</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">赵老师<br>物理实验</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">陈老师<br>化学原理</div>
                                </div>
                                <div class="schedule-cell"></div>
                                <div class="schedule-cell"></div>

                                <div class="schedule-time">10:00<br>11:40</div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">张教授<br>线性代数</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">刘老师<br>大学语文</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">王老师<br>数据结构</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item leave">赵老师<br>请假</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">李老师<br>英语写作</div>
                                </div>
                                <div class="schedule-cell"></div>
                                <div class="schedule-cell"></div>

                                <div class="schedule-time">14:00<br>15:40</div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">张教授<br>概率统计</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">孙老师<br>体育课</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">王老师<br>算法设计</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item training">全体教师<br>培训</div>
                                </div>
                                <div class="schedule-cell">
                                    <div class="schedule-item">陈老师<br>选修课</div>
                                </div>
                                <div class="schedule-cell"></div>
                                <div class="schedule-cell"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 考勤记录 -->
                <div class="tab-content" id="attendance-record">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">教师考勤记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="attendance-table">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>教师姓名</th>
                                        <th>签到时间</th>
                                        <th>签退时间</th>
                                        <th>工作时长</th>
                                        <th>状态</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-06-25</td>
                                        <td>张教授</td>
                                        <td>07:55:30</td>
                                        <td>17:30:15</td>
                                        <td>9小时35分</td>
                                        <td><span class="status-badge status-present">正常</span></td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25</td>
                                        <td>李老师</td>
                                        <td>08:05:20</td>
                                        <td>17:25:40</td>
                                        <td>9小时20分</td>
                                        <td><span class="status-badge status-late">迟到</span></td>
                                        <td>迟到5分钟</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25</td>
                                        <td>王老师</td>
                                        <td>08:00:10</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td><span class="status-badge status-present">在岗</span></td>
                                        <td>未签退</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-25</td>
                                        <td>赵老师</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td><span class="status-badge status-leave">请假</span></td>
                                        <td>病假已批准</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-24</td>
                                        <td>陈老师</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td><span class="status-badge status-absent">缺勤</span></td>
                                        <td>未请假</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 假勤管理 -->
                <div class="tab-content" id="leave-management">
                    <div class="leave-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-file-alt"></i> 请假申请审批
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="教师姓名" readonly value="当前登录用户">
                            </div>
                            <div class="form-group">
                                <label class="form-label">请假类型</label>
                                <select class="form-control">
                                    <option>病假</option>
                                    <option>事假</option>
                                    <option>年假</option>
                                    <option>调休</option>
                                    <option>其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">请假原因</label>
                            <textarea class="form-control" rows="4" placeholder="请详细说明请假原因..."></textarea>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-paper-plane"></i> 提交申请
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">请假申请记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="attendance-table">
                                <thead>
                                    <tr>
                                        <th>申请人</th>
                                        <th>请假类型</th>
                                        <th>请假时间</th>
                                        <th>天数</th>
                                        <th>申请时间</th>
                                        <th>审批状态</th>
                                        <th>审批人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>赵老师</td>
                                        <td>病假</td>
                                        <td>2024-06-25 至 2024-06-26</td>
                                        <td>2天</td>
                                        <td>2024-06-24</td>
                                        <td><span class="status-badge status-present">已批准</span></td>
                                        <td>院长</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>孙老师</td>
                                        <td>事假</td>
                                        <td>2024-06-28 至 2024-06-28</td>
                                        <td>1天</td>
                                        <td>2024-06-25</td>
                                        <td><span class="status-badge status-late">待审批</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">审批</button>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 绩效统计 -->
                <div class="tab-content" id="performance">
                    <div class="performance-card">
                        <div class="performance-header">
                            <h3>教师绩效评估</h3>
                            <div class="performance-score">85.6</div>
                        </div>
                        <div class="performance-metrics">
                            <div class="metric-item">
                                <div class="metric-value">96.8%</div>
                                <div class="metric-label">出勤率</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">240</div>
                                <div class="metric-label">本月工时</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">4.7</div>
                                <div class="metric-label">教学评分</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">12</div>
                                <div class="metric-label">培训次数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">工时统计图表</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 16px;">
                                <i class="fas fa-chart-bar" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>工时统计图表加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 节假日管理 -->
                <div class="tab-content" id="holiday-calendar">
                    <div class="holiday-calendar">
                        <div class="calendar-header">
                            <h3>2024年6月 节假日安排</h3>
                            <div class="calendar-nav">
                                <button><i class="fas fa-chevron-left"></i></button>
                                <button>今天</button>
                                <button><i class="fas fa-chevron-right"></i></button>
                            </div>
                        </div>
                        <div class="calendar-grid">
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">日</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">一</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">二</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">三</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">四</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">五</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">六</div>

                            <div class="calendar-day"><div class="day-number">1</div></div>
                            <div class="calendar-day workday"><div class="day-number">2</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">3</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">4</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">5</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">6</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day"><div class="day-number">7</div></div>

                            <div class="calendar-day"><div class="day-number">8</div></div>
                            <div class="calendar-day holiday"><div class="day-number">9</div><div class="day-status">端午节</div></div>
                            <div class="calendar-day holiday"><div class="day-number">10</div><div class="day-status">端午假期</div></div>
                            <div class="calendar-day workday"><div class="day-number">11</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">12</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">13</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day"><div class="day-number">14</div></div>

                            <div class="calendar-day"><div class="day-number">15</div></div>
                            <div class="calendar-day workday"><div class="day-number">16</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">17</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">18</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">19</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">20</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day"><div class="day-number">21</div></div>

                            <div class="calendar-day"><div class="day-number">22</div></div>
                            <div class="calendar-day workday"><div class="day-number">23</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">24</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day today workday"><div class="day-number">25</div><div class="day-status">今天</div></div>
                            <div class="calendar-day workday"><div class="day-number">26</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day workday"><div class="day-number">27</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day"><div class="day-number">28</div></div>

                            <div class="calendar-day"><div class="day-number">29</div></div>
                            <div class="calendar-day workday"><div class="day-number">30</div><div class="day-status">工作日</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">1</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">2</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">3</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">4</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">5</div></div>
                        </div>
                        <div style="margin-top: 20px; display: flex; gap: 20px; justify-content: center; font-size: 14px;">
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #3498db; border-radius: 2px; margin-right: 5px;"></span>工作日</div>
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #e74c3c; border-radius: 2px; margin-right: 5px;"></span>节假日</div>
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #667eea; border-radius: 2px; margin-right: 5px;"></span>今天</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('教师管理 > 教师考勤排班');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false });
            const dateStr = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            document.getElementById('current-time').textContent = timeStr;
            document.getElementById('current-date').textContent = dateStr;
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();

        // 教师签到
        function teacherCheckin() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('签到成功！', 'success');
            }, 1500);
        }

        // 教师签退
        function teacherCheckout() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('签退成功！今日工作辛苦了！', 'success');
            }, 1500);
        }

        // 请假申请提交
        document.querySelector('.leave-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('请假申请提交成功，等待审批', 'success');
            }, 1000);
        });

        // 排班表点击
        document.querySelectorAll('.schedule-cell').forEach(cell => {
            cell.addEventListener('click', function() {
                if (this.children.length === 0) {
                    SmartCampus.showMessage('点击空白时段可以安排课程', 'info');
                } else {
                    SmartCampus.showMessage('查看课程详情', 'info');
                }
            });
        });

        // 日历日期点击
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.addEventListener('click', function() {
                const dayNum = this.querySelector('.day-number')?.textContent;
                if (dayNum) {
                    SmartCampus.showMessage(`查看${dayNum}日的详细安排`, 'info');
                }
            });
        });
    </script>
</body>
</html>
