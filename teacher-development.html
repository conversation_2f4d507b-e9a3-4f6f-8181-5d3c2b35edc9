<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师发展档案 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .development-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .teacher-profile {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .teacher-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
        }

        .teacher-info h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .teacher-meta {
            opacity: 0.9;
            font-size: 14px;
        }

        .development-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .development-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #667eea;
            background: linear-gradient(135deg, rgba(102,126,234,0.1), rgba(118,75,162,0.1));
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .activity-timeline {
            position: relative;
            padding-left: 30px;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }

        .activity-item {
            position: relative;
            margin-bottom: 25px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .activity-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #667eea;
        }

        .activity-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .activity-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .activity-desc {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .activity-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .activity-tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .research-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .research-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .research-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .research-type {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 15px;
        }

        .research-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .research-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .meta-label {
            color: #666;
        }

        .meta-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .research-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-reviewing {
            background: #fff3cd;
            color: #856404;
        }

        .status-draft {
            background: #e2e3e5;
            color: #495057;
        }

        .award-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .award-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .award-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }

        .award-icon {
            font-size: 48px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .award-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .award-org {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .award-date {
            color: #999;
            font-size: 12px;
        }

        .education-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .education-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .education-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .education-table tr:hover {
            background: #f8f9fa;
        }

        .plan-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .achievement-summary {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <!-- 教师档案头部 -->
                <div class="development-header">
                    <div class="teacher-profile">
                        <div class="teacher-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="teacher-info">
                            <h2>张教授 发展档案</h2>
                            <div class="teacher-meta">
                                计算机科学与技术学院 | 教授 | 博士生导师<br>
                                入职时间：2015年9月 | 工号：T2024001
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发展统计 -->
                <div class="development-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">发表论文</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">科研项目</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <div class="stat-number">5</div>
                        <div class="stat-label">获得奖项</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">培训课程</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">45</div>
                        <div class="stat-label">指导学生</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="development-tabs">
                    <button class="tab-btn active" data-tab="teaching-activities">
                        <i class="fas fa-chalkboard-teacher"></i> 教学活动
                    </button>
                    <button class="tab-btn" data-tab="research-achievements">
                        <i class="fas fa-flask"></i> 科研成果
                    </button>
                    <button class="tab-btn" data-tab="awards-honors">
                        <i class="fas fa-trophy"></i> 评优记录
                    </button>
                    <button class="tab-btn" data-tab="continuing-education">
                        <i class="fas fa-certificate"></i> 继续教育
                    </button>
                    <button class="tab-btn" data-tab="development-plan">
                        <i class="fas fa-route"></i> 发展规划
                    </button>
                    <button class="tab-btn" data-tab="achievement-summary">
                        <i class="fas fa-chart-pie"></i> 成就统计
                    </button>
                </div>

                <!-- 教学活动 -->
                <div class="tab-content active" id="teaching-activities">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">教学活动记录</h3>
                        </div>
                        <div class="card-body">
                            <div class="activity-timeline">
                                <div class="activity-item">
                                    <div class="activity-date">2024年6月20日</div>
                                    <div class="activity-title">《人工智能基础》课程教学</div>
                                    <div class="activity-desc">
                                        为计算机科学专业本科生讲授人工智能基础课程，涵盖机器学习、深度学习等核心内容。学生反馈良好，课堂互动积极。
                                    </div>
                                    <div class="activity-tags">
                                        <span class="activity-tag">本科教学</span>
                                        <span class="activity-tag">人工智能</span>
                                        <span class="activity-tag">理论课程</span>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-date">2024年6月15日</div>
                                    <div class="activity-title">研究生学术指导</div>
                                    <div class="activity-desc">
                                        指导5名研究生进行学术研究，讨论论文写作和实验设计。其中2名学生的论文已投稿至国际会议。
                                    </div>
                                    <div class="activity-tags">
                                        <span class="activity-tag">研究生指导</span>
                                        <span class="activity-tag">学术研究</span>
                                        <span class="activity-tag">论文指导</span>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-date">2024年6月10日</div>
                                    <div class="activity-title">教学方法研讨会</div>
                                    <div class="activity-desc">
                                        参加学院组织的教学方法研讨会，分享"翻转课堂在计算机教学中的应用"经验，获得同行好评。
                                    </div>
                                    <div class="activity-tags">
                                        <span class="activity-tag">教学研讨</span>
                                        <span class="activity-tag">经验分享</span>
                                        <span class="activity-tag">翻转课堂</span>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-date">2024年6月5日</div>
                                    <div class="activity-title">实验课程设计</div>
                                    <div class="activity-desc">
                                        设计并实施《机器学习实验》课程，包含10个实验项目，帮助学生掌握实际编程技能。
                                    </div>
                                    <div class="activity-tags">
                                        <span class="activity-tag">实验教学</span>
                                        <span class="activity-tag">课程设计</span>
                                        <span class="activity-tag">实践能力</span>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-date">2024年5月28日</div>
                                    <div class="activity-title">学生竞赛指导</div>
                                    <div class="activity-desc">
                                        指导学生参加全国大学生计算机设计大赛，团队获得省级一等奖，并入围国赛。
                                    </div>
                                    <div class="activity-tags">
                                        <span class="activity-tag">竞赛指导</span>
                                        <span class="activity-tag">获奖项目</span>
                                        <span class="activity-tag">实践创新</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 科研成果 -->
                <div class="tab-content" id="research-achievements">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">科研成果管理</h3>
                        </div>
                        <div class="card-body">
                            <div class="research-grid">
                                <div class="research-card">
                                    <div class="research-type">SCI论文</div>
                                    <div class="research-title">Deep Learning Approaches for Natural Language Processing in Educational Systems</div>
                                    <div class="research-meta">
                                        <div class="meta-item">
                                            <span class="meta-label">期刊</span>
                                            <span class="meta-value">IEEE Transactions on Education</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">影响因子</span>
                                            <span class="meta-value">3.756</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">发表时间</span>
                                            <span class="meta-value">2024年3月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">状态</span>
                                            <span class="research-status status-published">已发表</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="research-card">
                                    <div class="research-type">国家级项目</div>
                                    <div class="research-title">基于人工智能的智慧教育平台关键技术研究</div>
                                    <div class="research-meta">
                                        <div class="meta-item">
                                            <span class="meta-label">项目来源</span>
                                            <span class="meta-value">国家自然科学基金</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">项目编号</span>
                                            <span class="meta-value">62177001</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">项目周期</span>
                                            <span class="meta-value">2022-2025</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">经费</span>
                                            <span class="meta-value">58万元</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="research-card">
                                    <div class="research-type">发明专利</div>
                                    <div class="research-title">一种基于深度学习的智能教学评价方法及系统</div>
                                    <div class="research-meta">
                                        <div class="meta-item">
                                            <span class="meta-label">专利号</span>
                                            <span class="meta-value">ZL202410123456.7</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">申请时间</span>
                                            <span class="meta-value">2024年1月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">授权时间</span>
                                            <span class="meta-value">2024年5月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">状态</span>
                                            <span class="research-status status-published">已授权</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="research-card">
                                    <div class="research-type">会议论文</div>
                                    <div class="research-title">Intelligent Tutoring Systems: A Comprehensive Survey of Recent Advances</div>
                                    <div class="research-meta">
                                        <div class="meta-item">
                                            <span class="meta-label">会议</span>
                                            <span class="meta-value">ICML 2024</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">会议级别</span>
                                            <span class="meta-value">CCF-A类</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">投稿时间</span>
                                            <span class="meta-value">2024年2月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">状态</span>
                                            <span class="research-status status-reviewing">审稿中</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="research-card">
                                    <div class="research-type">软件著作权</div>
                                    <div class="research-title">智慧校园教学管理系统V1.0</div>
                                    <div class="research-meta">
                                        <div class="meta-item">
                                            <span class="meta-label">登记号</span>
                                            <span class="meta-value">2024SR0123456</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">开发完成日期</span>
                                            <span class="meta-value">2024年4月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">首次发表日期</span>
                                            <span class="meta-value">2024年5月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">状态</span>
                                            <span class="research-status status-published">已登记</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="research-card">
                                    <div class="research-type">学术专著</div>
                                    <div class="research-title">人工智能在教育中的应用与实践</div>
                                    <div class="research-meta">
                                        <div class="meta-item">
                                            <span class="meta-label">出版社</span>
                                            <span class="meta-value">清华大学出版社</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">ISBN</span>
                                            <span class="meta-value">978-7-302-12345-6</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">预计出版</span>
                                            <span class="meta-value">2024年8月</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="meta-label">状态</span>
                                            <span class="research-status status-draft">编写中</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评优记录 -->
                <div class="tab-content" id="awards-honors">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">评优记录系统</h3>
                        </div>
                        <div class="card-body">
                            <div class="award-grid">
                                <div class="award-card">
                                    <div class="award-icon">
                                        <i class="fas fa-medal"></i>
                                    </div>
                                    <div class="award-title">优秀教师</div>
                                    <div class="award-org">教育部</div>
                                    <div class="award-date">2024年9月</div>
                                </div>

                                <div class="award-card">
                                    <div class="award-icon">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                    <div class="award-title">教学成果一等奖</div>
                                    <div class="award-org">省教育厅</div>
                                    <div class="award-date">2024年6月</div>
                                </div>

                                <div class="award-card">
                                    <div class="award-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="award-title">科技进步二等奖</div>
                                    <div class="award-org">省科技厅</div>
                                    <div class="award-date">2023年12月</div>
                                </div>

                                <div class="award-card">
                                    <div class="award-icon">
                                        <i class="fas fa-crown"></i>
                                    </div>
                                    <div class="award-title">青年学者奖</div>
                                    <div class="award-org">中国计算机学会</div>
                                    <div class="award-date">2023年8月</div>
                                </div>

                                <div class="award-card">
                                    <div class="award-icon">
                                        <i class="fas fa-gem"></i>
                                    </div>
                                    <div class="award-title">优秀指导教师</div>
                                    <div class="award-org">全国大学生竞赛组委会</div>
                                    <div class="award-date">2023年5月</div>
                                </div>

                                <div class="award-card">
                                    <div class="award-icon">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                    <div class="award-title">师德标兵</div>
                                    <div class="award-org">学校</div>
                                    <div class="award-date">2022年12月</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 继续教育 -->
                <div class="tab-content" id="continuing-education">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">继续教育管理</h3>
                        </div>
                        <div class="card-body">
                            <table class="education-table">
                                <thead>
                                    <tr>
                                        <th>培训项目</th>
                                        <th>培训机构</th>
                                        <th>培训时间</th>
                                        <th>培训类型</th>
                                        <th>学时</th>
                                        <th>证书状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>人工智能前沿技术研修班</td>
                                        <td>清华大学</td>
                                        <td>2024年7月1日-7月15日</td>
                                        <td>学术研修</td>
                                        <td>80学时</td>
                                        <td><span class="research-status status-published">已获证书</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看证书</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>高等教育教学方法培训</td>
                                        <td>北京师范大学</td>
                                        <td>2024年5月10日-5月20日</td>
                                        <td>教学培训</td>
                                        <td>60学时</td>
                                        <td><span class="research-status status-published">已获证书</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看证书</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>科研项目管理与申报</td>
                                        <td>中科院</td>
                                        <td>2024年3月15日-3月25日</td>
                                        <td>科研培训</td>
                                        <td>40学时</td>
                                        <td><span class="research-status status-published">已获证书</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看证书</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>学术英语写作提升班</td>
                                        <td>外语教学与研究出版社</td>
                                        <td>2024年1月8日-1月18日</td>
                                        <td>语言培训</td>
                                        <td>50学时</td>
                                        <td><span class="research-status status-published">已获证书</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看证书</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>信息化教学能力提升</td>
                                        <td>教育部在线教育研究中心</td>
                                        <td>2023年11月1日-11月10日</td>
                                        <td>技能培训</td>
                                        <td>30学时</td>
                                        <td><span class="research-status status-published">已获证书</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看证书</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 发展规划 -->
                <div class="tab-content" id="development-plan">
                    <div class="plan-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-route"></i> 个人发展规划
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">规划周期</label>
                                <select class="form-control">
                                    <option>2024-2026年（3年规划）</option>
                                    <option>2024-2029年（5年规划）</option>
                                    <option>2024-2034年（10年规划）</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">发展方向</label>
                                <select class="form-control">
                                    <option>教学科研并重</option>
                                    <option>科研导向</option>
                                    <option>教学导向</option>
                                    <option>产学研结合</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">教学发展目标</label>
                            <textarea class="form-control" rows="3" placeholder="请描述您的教学发展目标...">1. 完善人工智能课程体系建设，开发3-5门新课程
2. 指导学生参加国际竞赛，争取获得国际奖项
3. 发表教学研究论文5篇以上，申请教学改革项目2项
4. 培养博士研究生10名，硕士研究生30名</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">科研发展目标</label>
                            <textarea class="form-control" rows="3" placeholder="请描述您的科研发展目标...">1. 在人工智能教育应用领域发表SCI论文15篇以上
2. 申请国家级科研项目2-3项，省部级项目5项以上
3. 获得发明专利10项，软件著作权5项
4. 出版学术专著2部，参与制定行业标准1项</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">能力提升计划</label>
                            <textarea class="form-control" rows="3" placeholder="请描述您的能力提升计划...">1. 参加国际学术会议，提升国际影响力
2. 完成高级管理培训，提升团队领导能力
3. 学习新兴技术，保持技术前沿性
4. 加强产学研合作，提升成果转化能力</textarea>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-save"></i> 保存规划
                            </button>
                            <button class="btn btn-primary" style="padding: 12px 40px; margin-left: 10px;">
                                <i class="fas fa-eye"></i> 预览规划
                            </button>
                        </div>
                    </div>

                    <!-- 规划进度跟踪 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">规划执行进度</h3>
                        </div>
                        <div class="card-body">
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span>教学目标完成度</span>
                                    <span style="font-weight: bold; color: #667eea;">75%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%;"></div>
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span>科研目标完成度</span>
                                    <span style="font-weight: bold; color: #667eea;">60%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 60%;"></div>
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span>能力提升完成度</span>
                                    <span style="font-weight: bold; color: #667eea;">85%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%;"></div>
                                </div>
                            </div>

                            <div>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span>整体规划完成度</span>
                                    <span style="font-weight: bold; color: #667eea;">73%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 73%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成就统计 -->
                <div class="tab-content" id="achievement-summary">
                    <div class="achievement-summary">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-chart-pie"></i> 学术成就统计
                        </h3>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <div class="summary-number">23</div>
                                <div class="summary-label">发表论文</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">156.8</div>
                                <div class="summary-label">总影响因子</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">342</div>
                                <div class="summary-label">论文引用</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">8</div>
                                <div class="summary-label">科研项目</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">285</div>
                                <div class="summary-label">项目经费(万)</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">12</div>
                                <div class="summary-label">发明专利</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">5</div>
                                <div class="summary-label">获得奖项</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-number">45</div>
                                <div class="summary-label">指导学生</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">年度成就趋势</h3>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 16px;">
                                <i class="fas fa-chart-line" style="font-size: 48px; opacity: 0.3; margin-right: 15px;"></i>
                                <span>成就趋势图表加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('教师管理 > 教师发展档案');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 保存规划
        document.querySelector('.plan-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('发展规划保存成功！', 'success');
            }, 1000);
        });

        // 预览规划
        document.querySelector('.plan-form .btn-primary').addEventListener('click', function() {
            SmartCampus.showMessage('正在生成规划预览...', 'info');
        });

        // 查看证书
        document.querySelectorAll('.education-table .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                SmartCampus.showMessage('正在加载证书详情...', 'info');
            });
        });

        // 科研卡片点击
        document.querySelectorAll('.research-card').forEach(card => {
            card.addEventListener('click', function() {
                SmartCampus.showMessage('查看详细信息...', 'info');
            });
        });

        // 奖项卡片点击
        document.querySelectorAll('.award-card').forEach(card => {
            card.addEventListener('click', function() {
                SmartCampus.showMessage('查看奖项详情...', 'info');
            });
        });
    </script>
</body>
</html>
