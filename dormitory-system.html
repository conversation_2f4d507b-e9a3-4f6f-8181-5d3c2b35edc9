<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宿舍管理系统 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .dormitory-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dormitory-stat {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .dormitory-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #9b59b6, #8e44ad);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .dormitory-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #9b59b6;
            background: linear-gradient(135deg, rgba(155,89,182,0.1), rgba(142,68,173,0.1));
            border-bottom-color: #9b59b6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .building-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .building-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .building-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .building-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .building-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .building-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: #d4edda;
            color: #155724;
        }

        .status-maintenance {
            background: #fff3cd;
            color: #856404;
        }

        .status-full {
            background: #f8d7da;
            color: #721c24;
        }

        .building-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .room-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 8px;
            margin-top: 15px;
        }

        .room-cell {
            aspect-ratio: 1;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .room-available {
            background: #d4edda;
            border-color: #2ecc71;
            color: #155724;
        }

        .room-occupied {
            background: #cce5ff;
            border-color: #3498db;
            color: #004085;
        }

        .room-maintenance {
            background: #fff3cd;
            border-color: #f39c12;
            color: #856404;
        }

        .room-cell:hover {
            transform: scale(1.05);
        }

        .student-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .student-table th {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .student-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .student-table tr:hover {
            background: #f8f9fa;
        }

        .visitor-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .power-control {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .power-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .power-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .power-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #9b59b6;
        }

        .power-status {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .power-toggle {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .power-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(155,89,182,0.3);
        }

        .lock-control {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .lock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .lock-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lock-info {
            flex: 1;
        }

        .lock-room {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .lock-status {
            font-size: 12px;
            color: #666;
        }

        .lock-btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lock-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(155,89,182,0.3);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-in {
            background: #d4edda;
            color: #155724;
        }

        .status-out {
            background: #f8d7da;
            color: #721c24;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-building"></i> 宿舍管理系统
                </h1>

                <!-- 宿舍统计 -->
                <div class="dormitory-dashboard">
                    <div class="dormitory-stat">
                        <div class="stat-icon">
                            <i class="fas fa-bed"></i>
                        </div>
                        <div class="stat-number">1,248</div>
                        <div class="stat-label">总床位数</div>
                    </div>
                    <div class="dormitory-stat">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">1,156</div>
                        <div class="stat-label">入住学生</div>
                    </div>
                    <div class="dormitory-stat">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-number">92.6%</div>
                        <div class="stat-label">入住率</div>
                    </div>
                    <div class="dormitory-stat">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">3</div>
                        <div class="stat-label">维修房间</div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="dormitory-tabs">
                    <button class="tab-btn active" data-tab="room-allocation">
                        <i class="fas fa-home"></i> 房间分配
                    </button>
                    <button class="tab-btn" data-tab="student-management">
                        <i class="fas fa-user-friends"></i> 住宿管理
                    </button>
                    <button class="tab-btn" data-tab="visitor-registration">
                        <i class="fas fa-user-check"></i> 访客登记
                    </button>
                    <button class="tab-btn" data-tab="power-control">
                        <i class="fas fa-bolt"></i> 电控系统
                    </button>
                    <button class="tab-btn" data-tab="smart-lock">
                        <i class="fas fa-lock"></i> 智能门锁
                    </button>
                </div>

                <!-- 房间分配 -->
                <div class="tab-content active" id="room-allocation">
                    <div class="building-grid">
                        <div class="building-card">
                            <div class="building-header">
                                <div class="building-name">学生宿舍1号楼</div>
                                <div class="building-status status-normal">正常</div>
                            </div>
                            <div class="building-info">
                                <div class="info-item">
                                    <span class="info-label">总房间</span>
                                    <span class="info-value">120间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">已入住</span>
                                    <span class="info-value">108间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">空闲</span>
                                    <span class="info-value">10间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">维修</span>
                                    <span class="info-value">2间</span>
                                </div>
                            </div>
                            <div class="room-grid">
                                <div class="room-cell room-occupied">101</div>
                                <div class="room-cell room-occupied">102</div>
                                <div class="room-cell room-available">103</div>
                                <div class="room-cell room-occupied">104</div>
                                <div class="room-cell room-maintenance">105</div>
                                <div class="room-cell room-occupied">106</div>
                                <div class="room-cell room-occupied">107</div>
                                <div class="room-cell room-available">108</div>
                                <div class="room-cell room-occupied">109</div>
                                <div class="room-cell room-occupied">110</div>
                                <div class="room-cell room-occupied">111</div>
                                <div class="room-cell room-occupied">112</div>
                            </div>
                        </div>

                        <div class="building-card">
                            <div class="building-header">
                                <div class="building-name">学生宿舍2号楼</div>
                                <div class="building-status status-normal">正常</div>
                            </div>
                            <div class="building-info">
                                <div class="info-item">
                                    <span class="info-label">总房间</span>
                                    <span class="info-value">120间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">已入住</span>
                                    <span class="info-value">115间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">空闲</span>
                                    <span class="info-value">4间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">维修</span>
                                    <span class="info-value">1间</span>
                                </div>
                            </div>
                            <div class="room-grid">
                                <div class="room-cell room-occupied">201</div>
                                <div class="room-cell room-occupied">202</div>
                                <div class="room-cell room-occupied">203</div>
                                <div class="room-cell room-available">204</div>
                                <div class="room-cell room-occupied">205</div>
                                <div class="room-cell room-occupied">206</div>
                                <div class="room-cell room-maintenance">207</div>
                                <div class="room-cell room-occupied">208</div>
                                <div class="room-cell room-occupied">209</div>
                                <div class="room-cell room-available">210</div>
                                <div class="room-cell room-occupied">211</div>
                                <div class="room-cell room-occupied">212</div>
                            </div>
                        </div>

                        <div class="building-card">
                            <div class="building-header">
                                <div class="building-name">学生宿舍3号楼</div>
                                <div class="building-status status-full">满员</div>
                            </div>
                            <div class="building-info">
                                <div class="info-item">
                                    <span class="info-label">总房间</span>
                                    <span class="info-value">120间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">已入住</span>
                                    <span class="info-value">120间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">空闲</span>
                                    <span class="info-value">0间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">维修</span>
                                    <span class="info-value">0间</span>
                                </div>
                            </div>
                            <div class="room-grid">
                                <div class="room-cell room-occupied">301</div>
                                <div class="room-cell room-occupied">302</div>
                                <div class="room-cell room-occupied">303</div>
                                <div class="room-cell room-occupied">304</div>
                                <div class="room-cell room-occupied">305</div>
                                <div class="room-cell room-occupied">306</div>
                                <div class="room-cell room-occupied">307</div>
                                <div class="room-cell room-occupied">308</div>
                                <div class="room-cell room-occupied">309</div>
                                <div class="room-cell room-occupied">310</div>
                                <div class="room-cell room-occupied">311</div>
                                <div class="room-cell room-occupied">312</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 住宿管理 -->
                <div class="tab-content" id="student-management">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">学生住宿信息</h3>
                        </div>
                        <div class="card-body">
                            <table class="student-table">
                                <thead>
                                    <tr>
                                        <th>学号</th>
                                        <th>姓名</th>
                                        <th>宿舍楼</th>
                                        <th>房间号</th>
                                        <th>床位号</th>
                                        <th>入住时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024001001</td>
                                        <td>张三</td>
                                        <td>1号楼</td>
                                        <td>101</td>
                                        <td>A</td>
                                        <td>2024-09-01</td>
                                        <td><span class="status-badge status-in">在住</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">调换</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2024001002</td>
                                        <td>李四</td>
                                        <td>1号楼</td>
                                        <td>101</td>
                                        <td>B</td>
                                        <td>2024-09-01</td>
                                        <td><span class="status-badge status-in">在住</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">调换</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2024001003</td>
                                        <td>王五</td>
                                        <td>2号楼</td>
                                        <td>204</td>
                                        <td>A</td>
                                        <td>2024-09-01</td>
                                        <td><span class="status-badge status-out">退宿</span></td>
                                        <td>
                                            <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">归档</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2024001004</td>
                                        <td>赵六</td>
                                        <td>3号楼</td>
                                        <td>301</td>
                                        <td>C</td>
                                        <td>2024-09-01</td>
                                        <td><span class="status-badge status-pending">申请调换</span></td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">审批</button>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 访客登记 -->
                <div class="tab-content" id="visitor-registration">
                    <div class="visitor-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-user-plus"></i> 访客登记
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">访客姓名</label>
                                <input type="text" class="form-control" placeholder="请输入访客姓名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">身份证号</label>
                                <input type="text" class="form-control" placeholder="请输入身份证号">
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话</label>
                                <input type="tel" class="form-control" placeholder="请输入联系电话">
                            </div>
                            <div class="form-group">
                                <label class="form-label">被访学生</label>
                                <input type="text" class="form-control" placeholder="请输入学生姓名或学号">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">访问宿舍</label>
                                <select class="form-control">
                                    <option>1号楼</option>
                                    <option>2号楼</option>
                                    <option>3号楼</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">房间号</label>
                                <input type="text" class="form-control" placeholder="如：101">
                            </div>
                            <div class="form-group">
                                <label class="form-label">访问时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">预计停留</label>
                                <select class="form-control">
                                    <option>1小时内</option>
                                    <option>2小时内</option>
                                    <option>半天</option>
                                    <option>全天</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">访问事由</label>
                            <textarea class="form-control" rows="3" placeholder="请简要说明访问事由..."></textarea>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-check"></i> 登记访客
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">今日访客记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="student-table">
                                <thead>
                                    <tr>
                                        <th>访客姓名</th>
                                        <th>联系电话</th>
                                        <th>被访学生</th>
                                        <th>访问宿舍</th>
                                        <th>登记时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>张家长</td>
                                        <td>138****1234</td>
                                        <td>张三</td>
                                        <td>1号楼101</td>
                                        <td>2024-06-25 14:30</td>
                                        <td><span class="status-badge status-in">访问中</span></td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">离开登记</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>李朋友</td>
                                        <td>139****5678</td>
                                        <td>李四</td>
                                        <td>1号楼101</td>
                                        <td>2024-06-25 10:15</td>
                                        <td><span class="status-badge status-out">已离开</span></td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 电控系统 -->
                <div class="tab-content" id="power-control">
                    <div class="power-control">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-bolt"></i> 宿舍电控管理
                        </h3>
                        <div class="power-grid">
                            <div class="power-item">
                                <div class="power-icon">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="power-status">1号楼101室</div>
                                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">照明系统</div>
                                <button class="power-toggle">开启</button>
                            </div>

                            <div class="power-item">
                                <div class="power-icon">
                                    <i class="fas fa-plug"></i>
                                </div>
                                <div class="power-status">1号楼101室</div>
                                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">插座电源</div>
                                <button class="power-toggle">关闭</button>
                            </div>

                            <div class="power-item">
                                <div class="power-icon">
                                    <i class="fas fa-snowflake"></i>
                                </div>
                                <div class="power-status">1号楼101室</div>
                                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">空调系统</div>
                                <button class="power-toggle">开启</button>
                            </div>

                            <div class="power-item">
                                <div class="power-icon">
                                    <i class="fas fa-wifi"></i>
                                </div>
                                <div class="power-status">1号楼101室</div>
                                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">网络设备</div>
                                <button class="power-toggle">正常</button>
                            </div>

                            <div class="power-item">
                                <div class="power-icon">
                                    <i class="fas fa-tint"></i>
                                </div>
                                <div class="power-status">1号楼101室</div>
                                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">热水器</div>
                                <button class="power-toggle">开启</button>
                            </div>

                            <div class="power-item">
                                <div class="power-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="power-status">1号楼105室</div>
                                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">电路故障</div>
                                <button class="power-toggle" style="background: #e74c3c;">维修中</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能门锁 -->
                <div class="tab-content" id="smart-lock">
                    <div class="lock-control">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-lock"></i> 智能门锁管理
                        </h3>
                        <div class="lock-grid">
                            <div class="lock-item">
                                <div class="lock-info">
                                    <div class="lock-room">1号楼101室</div>
                                    <div class="lock-status">门锁状态：已锁定 | 电量：85%</div>
                                </div>
                                <button class="lock-btn">远程开锁</button>
                            </div>

                            <div class="lock-item">
                                <div class="lock-info">
                                    <div class="lock-room">1号楼102室</div>
                                    <div class="lock-status">门锁状态：未锁定 | 电量：92%</div>
                                </div>
                                <button class="lock-btn">远程锁定</button>
                            </div>

                            <div class="lock-item">
                                <div class="lock-info">
                                    <div class="lock-room">1号楼103室</div>
                                    <div class="lock-status">门锁状态：已锁定 | 电量：15%</div>
                                </div>
                                <button class="lock-btn" style="background: #f39c12;">更换电池</button>
                            </div>

                            <div class="lock-item">
                                <div class="lock-info">
                                    <div class="lock-room">1号楼104室</div>
                                    <div class="lock-status">门锁状态：已锁定 | 电量：78%</div>
                                </div>
                                <button class="lock-btn">远程开锁</button>
                            </div>

                            <div class="lock-item">
                                <div class="lock-info">
                                    <div class="lock-room">1号楼105室</div>
                                    <div class="lock-status">门锁状态：故障 | 电量：0%</div>
                                </div>
                                <button class="lock-btn" style="background: #e74c3c;">维修中</button>
                            </div>

                            <div class="lock-item">
                                <div class="lock-info">
                                    <div class="lock-room">1号楼106室</div>
                                    <div class="lock-status">门锁状态：已锁定 | 电量：95%</div>
                                </div>
                                <button class="lock-btn">远程开锁</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('校园服务 > 宿舍管理系统');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 房间点击
        document.querySelectorAll('.room-cell').forEach(cell => {
            cell.addEventListener('click', function() {
                const roomNumber = this.textContent;
                const status = this.classList.contains('room-available') ? '空闲' :
                              this.classList.contains('room-occupied') ? '已入住' : '维修中';
                SmartCampus.showMessage(`房间${roomNumber} - 状态：${status}`, 'info');
            });
        });

        // 访客登记
        document.querySelector('.visitor-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('访客登记成功！', 'success');
            }, 1000);
        });

        // 电控操作
        document.querySelectorAll('.power-toggle').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent === '维修中') return;

                const action = this.textContent === '开启' ? '关闭' : '开启';
                this.textContent = action;
                SmartCampus.showMessage(`电控操作成功：${action}`, 'success');
            });
        });

        // 门锁操作
        document.querySelectorAll('.lock-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent === '维修中' || this.textContent === '更换电池') {
                    SmartCampus.showMessage('设备需要维护，请联系管理员', 'warning');
                    return;
                }

                const action = this.textContent;
                SmartCampus.showMessage(`${action}操作成功`, 'success');
            });
        });

        // 表格操作
        document.querySelectorAll('table .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                SmartCampus.showMessage(`正在执行${action}操作...`, 'info');
            });
        });
    </script>
</body>
</html>
