<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧校园管理系统 - 首页</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        /* 首页特定样式 */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card.students {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.teachers {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card.courses {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card.attendance {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }

        .recent-activities {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .weather-widget {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .weather-temp {
            font-size: 36px;
            font-weight: bold;
            margin: 10px 0;
        }

        .weather-desc {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt"></i> 数据看板
                </h1>

                <!-- 统计数据卡片 -->
                <div class="dashboard-stats">
                    <div class="stat-card students">
                        <div class="stat-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="stat-number">12,456</div>
                        <div class="stat-label">在校学生</div>
                    </div>
                    <div class="stat-card teachers">
                        <div class="stat-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="stat-number">856</div>
                        <div class="stat-label">教职工</div>
                    </div>
                    <div class="stat-card courses">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-number">342</div>
                        <div class="stat-label">开设课程</div>
                    </div>
                    <div class="stat-card attendance">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">96.8%</div>
                        <div class="stat-label">今日出勤率</div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i> 快捷操作
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <div class="action-card" onclick="location.href='student-info.html'">
                                <div class="action-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <h4>学生管理</h4>
                                <p>学生信息录入与管理</p>
                            </div>
                            <div class="action-card" onclick="location.href='teaching-resources.html'">
                                <div class="action-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h4>课程安排</h4>
                                <p>智能排课与教室管理</p>
                            </div>
                            <div class="action-card" onclick="location.href='academic-affairs.html'">
                                <div class="action-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h4>成绩管理</h4>
                                <p>成绩录入与统计分析</p>
                            </div>
                            <div class="action-card" onclick="location.href='student-attendance.html'">
                                <div class="action-icon">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                                <h4>考勤打卡</h4>
                                <p>学生教师考勤管理</p>
                            </div>
                            <div class="action-card" onclick="location.href='security-system.html'">
                                <div class="action-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h4>安防监控</h4>
                                <p>校园安全监控系统</p>
                            </div>
                            <div class="action-card" onclick="location.href='logistics-system.html'">
                                <div class="action-icon">
                                    <i class="fas fa-wrench"></i>
                                </div>
                                <h4>报修服务</h4>
                                <p>设备报修与维护</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-2">
                    <!-- 数据图表 -->
                    <div class="chart-container">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-chart-bar"></i> 本月考勤统计
                        </h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-bar" style="font-size: 48px; opacity: 0.3;"></i>
                            <span style="margin-left: 15px;">图表数据加载中...</span>
                        </div>
                    </div>

                    <!-- 天气信息 -->
                    <div class="weather-widget">
                        <h3><i class="fas fa-cloud-sun"></i> 今日天气</h3>
                        <div class="weather-temp">22°C</div>
                        <div class="weather-desc">晴转多云 · 空气质量良好</div>
                        <div style="margin-top: 15px; font-size: 14px;">
                            <i class="fas fa-eye"></i> 能见度 15km &nbsp;&nbsp;
                            <i class="fas fa-tint"></i> 湿度 65%
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="recent-activities">
                    <h3 style="margin-bottom: 20px;">
                        <i class="fas fa-history"></i> 最近活动
                    </h3>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新增学生档案</div>
                            <div class="activity-time">张三 - 2024年3月15日 14:30</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">课程表更新</div>
                            <div class="activity-time">计算机科学系 - 2024年3月15日 13:45</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">设备报修</div>
                            <div class="activity-time">教学楼A座201教室投影仪 - 2024年3月15日 12:20</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">成绩录入完成</div>
                            <div class="activity-time">高等数学期中考试 - 2024年3月15日 11:15</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('首页 - 数据看板');

        // 模拟实时数据更新
        setInterval(() => {
            const attendanceCard = document.querySelector('.stat-card.attendance .stat-number');
            const currentRate = parseFloat(attendanceCard.textContent);
            const newRate = (currentRate + (Math.random() - 0.5) * 0.1).toFixed(1);
            attendanceCard.textContent = newRate + '%';
        }, 5000);

        // 添加页面加载完成提示
        window.addEventListener('load', () => {
            SmartCampus.showMessage('欢迎使用智慧校园管理系统！', 'success');
        });
    </script>
</body>
</html>
