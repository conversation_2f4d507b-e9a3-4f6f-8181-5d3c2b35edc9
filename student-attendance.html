<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生考勤系统 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .attendance-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .attendance-stat {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .attendance-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .checkin-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 30px rgba(102,126,234,0.3);
        }

        .checkin-time {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
        }

        .checkin-date {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .checkin-methods {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .checkin-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkin-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        .attendance-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .attendance-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .attendance-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .attendance-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-present {
            background: #d4edda;
            color: #155724;
        }

        .status-late {
            background: #fff3cd;
            color: #856404;
        }

        .status-absent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-leave {
            background: #cce5ff;
            color: #004085;
        }

        .leave-form {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .calendar-view {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-nav {
            display: flex;
            gap: 10px;
        }

        .calendar-nav button {
            background: none;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-nav button:hover {
            border-color: #3498db;
            color: #3498db;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .calendar-day {
            background: white;
            padding: 15px 10px;
            text-align: center;
            min-height: 60px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-day:hover {
            background: #f8f9fa;
        }

        .calendar-day.today {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
        }

        .calendar-day.has-record {
            border-bottom: 3px solid #2ecc71;
        }

        .calendar-day.has-absence {
            border-bottom: 3px solid #e74c3c;
        }

        .day-number {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .day-status {
            font-size: 10px;
            opacity: 0.8;
        }

        .qr-scanner {
            background: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .qr-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #ccc;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            background: #f8f9fa;
        }

        .location-info {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .location-icon {
            font-size: 24px;
            opacity: 0.9;
        }

        .location-text {
            flex: 1;
        }

        .location-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .location-desc {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-clock"></i> 学生考勤系统
                </h1>

                <!-- 考勤统计面板 -->
                <div class="attendance-dashboard">
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-number">96.8%</div>
                        <div class="stat-label">本月出勤率</div>
                    </div>
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">正常出勤天数</div>
                    </div>
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">2</div>
                        <div class="stat-label">迟到次数</div>
                    </div>
                    <div class="attendance-stat">
                        <div class="stat-icon">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-number">1</div>
                        <div class="stat-label">缺勤次数</div>
                    </div>
                </div>

                <!-- 签到面板 -->
                <div class="checkin-panel">
                    <div class="checkin-time" id="current-time">08:45:32</div>
                    <div class="checkin-date" id="current-date">2024年6月25日 星期二</div>
                    <div class="checkin-methods">
                        <button class="checkin-btn" onclick="faceCheckin()">
                            <i class="fas fa-user"></i> 人脸签到
                        </button>
                        <button class="checkin-btn" onclick="cardCheckin()">
                            <i class="fas fa-id-card"></i> 刷卡签到
                        </button>
                        <button class="checkin-btn" onclick="qrCheckin()">
                            <i class="fas fa-qrcode"></i> 扫码签到
                        </button>
                        <button class="checkin-btn" onclick="gpsCheckin()">
                            <i class="fas fa-map-marker-alt"></i> 定位签到
                        </button>
                    </div>
                </div>

                <!-- 位置信息 -->
                <div class="location-info">
                    <div class="location-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="location-text">
                        <div class="location-title">当前位置</div>
                        <div class="location-desc">教学楼A座101教室 - 距离签到点15米</div>
                    </div>
                    <div>
                        <span style="background: rgba(255,255,255,0.2); padding: 5px 12px; border-radius: 20px; font-size: 12px;">
                            <i class="fas fa-wifi"></i> 校园网络
                        </span>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="attendance-tabs">
                    <button class="tab-btn active" data-tab="attendance-record">
                        <i class="fas fa-list"></i> 考勤记录
                    </button>
                    <button class="tab-btn" data-tab="leave-request">
                        <i class="fas fa-file-alt"></i> 请假申请
                    </button>
                    <button class="tab-btn" data-tab="calendar-view">
                        <i class="fas fa-calendar"></i> 日历视图
                    </button>
                    <button class="tab-btn" data-tab="qr-scanner">
                        <i class="fas fa-qrcode"></i> 扫码签到
                    </button>
                </div>

                <!-- 考勤记录 -->
                <div class="tab-content active" id="attendance-record">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最近考勤记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="attendance-table">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>课程</th>
                                        <th>签到时间</th>
                                        <th>签退时间</th>
                                        <th>状态</th>
                                        <th>签到方式</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-06-25</td>
                                        <td>高等数学</td>
                                        <td>08:00:15</td>
                                        <td>09:40:22</td>
                                        <td><span class="status-badge status-present">正常</span></td>
                                        <td>人脸识别</td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-24</td>
                                        <td>大学英语</td>
                                        <td>10:05:30</td>
                                        <td>11:40:18</td>
                                        <td><span class="status-badge status-late">迟到</span></td>
                                        <td>校园卡</td>
                                        <td>迟到5分钟</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-23</td>
                                        <td>计算机基础</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td><span class="status-badge status-absent">缺勤</span></td>
                                        <td>-</td>
                                        <td>未签到</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-22</td>
                                        <td>体育课</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td><span class="status-badge status-leave">请假</span></td>
                                        <td>-</td>
                                        <td>病假已批准</td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-21</td>
                                        <td>线性代数</td>
                                        <td>14:00:08</td>
                                        <td>15:40:35</td>
                                        <td><span class="status-badge status-present">正常</span></td>
                                        <td>二维码</td>
                                        <td>-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 请假申请 -->
                <div class="tab-content" id="leave-request">
                    <div class="leave-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-file-alt"></i> 请假申请
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">请假类型</label>
                                <select class="form-control">
                                    <option>病假</option>
                                    <option>事假</option>
                                    <option>公假</option>
                                    <option>其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">请假天数</label>
                                <input type="number" class="form-control" placeholder="自动计算" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">请假原因</label>
                            <textarea class="form-control" rows="4" placeholder="请详细说明请假原因..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">证明材料</label>
                            <input type="file" class="form-control" accept="image/*,.pdf">
                            <small style="color: #666; font-size: 12px;">支持图片和PDF格式，最大5MB</small>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn btn-success" style="padding: 12px 40px;">
                                <i class="fas fa-paper-plane"></i> 提交申请
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">请假记录</h3>
                        </div>
                        <div class="card-body">
                            <table class="attendance-table">
                                <thead>
                                    <tr>
                                        <th>申请时间</th>
                                        <th>请假类型</th>
                                        <th>请假时间</th>
                                        <th>天数</th>
                                        <th>审批状态</th>
                                        <th>审批人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-06-20</td>
                                        <td>病假</td>
                                        <td>2024-06-22 至 2024-06-22</td>
                                        <td>1天</td>
                                        <td><span class="status-badge status-present">已批准</span></td>
                                        <td>张老师</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2024-06-15</td>
                                        <td>事假</td>
                                        <td>2024-06-18 至 2024-06-19</td>
                                        <td>2天</td>
                                        <td><span class="status-badge status-late">审批中</span></td>
                                        <td>-</td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">撤回</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 日历视图 -->
                <div class="tab-content" id="calendar-view">
                    <div class="calendar-view">
                        <div class="calendar-header">
                            <h3>2024年6月 考勤日历</h3>
                            <div class="calendar-nav">
                                <button><i class="fas fa-chevron-left"></i></button>
                                <button>今天</button>
                                <button><i class="fas fa-chevron-right"></i></button>
                            </div>
                        </div>
                        <div class="calendar-grid">
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">日</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">一</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">二</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">三</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">四</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">五</div>
                            <div class="calendar-day" style="background: #f8f9fa; color: #666;">六</div>

                            <div class="calendar-day"><div class="day-number">1</div></div>
                            <div class="calendar-day has-record"><div class="day-number">2</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">3</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">4</div><div class="day-status">迟到</div></div>
                            <div class="calendar-day has-record"><div class="day-number">5</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">6</div><div class="day-status">正常</div></div>
                            <div class="calendar-day"><div class="day-number">7</div></div>

                            <div class="calendar-day"><div class="day-number">8</div></div>
                            <div class="calendar-day has-record"><div class="day-number">9</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">10</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">11</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">12</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">13</div><div class="day-status">正常</div></div>
                            <div class="calendar-day"><div class="day-number">14</div></div>

                            <div class="calendar-day"><div class="day-number">15</div></div>
                            <div class="calendar-day has-record"><div class="day-number">16</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">17</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">18</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">19</div><div class="day-status">正常</div></div>
                            <div class="calendar-day has-record"><div class="day-number">20</div><div class="day-status">正常</div></div>
                            <div class="calendar-day"><div class="day-number">21</div></div>

                            <div class="calendar-day"><div class="day-number">22</div></div>
                            <div class="calendar-day has-absence"><div class="day-number">23</div><div class="day-status">缺勤</div></div>
                            <div class="calendar-day has-record"><div class="day-number">24</div><div class="day-status">迟到</div></div>
                            <div class="calendar-day today has-record"><div class="day-number">25</div><div class="day-status">正常</div></div>
                            <div class="calendar-day"><div class="day-number">26</div></div>
                            <div class="calendar-day"><div class="day-number">27</div></div>
                            <div class="calendar-day"><div class="day-number">28</div></div>

                            <div class="calendar-day"><div class="day-number">29</div></div>
                            <div class="calendar-day"><div class="day-number">30</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">1</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">2</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">3</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">4</div></div>
                            <div class="calendar-day" style="color: #ccc;"><div class="day-number">5</div></div>
                        </div>
                        <div style="margin-top: 20px; display: flex; gap: 20px; justify-content: center; font-size: 14px;">
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #2ecc71; border-radius: 2px; margin-right: 5px;"></span>正常出勤</div>
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #f39c12; border-radius: 2px; margin-right: 5px;"></span>迟到</div>
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #e74c3c; border-radius: 2px; margin-right: 5px;"></span>缺勤</div>
                            <div><span style="display: inline-block; width: 12px; height: 12px; background: #3498db; border-radius: 2px; margin-right: 5px;"></span>今天</div>
                        </div>
                    </div>
                </div>

                <!-- 扫码签到 -->
                <div class="tab-content" id="qr-scanner">
                    <div class="qr-scanner">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-qrcode"></i> 二维码签到
                        </h3>
                        <div class="qr-placeholder">
                            <div style="text-align: center; color: #666;">
                                <i class="fas fa-camera" style="font-size: 48px; margin-bottom: 10px;"></i>
                                <p>请将二维码对准摄像头</p>
                            </div>
                        </div>
                        <p style="color: #666; margin-bottom: 20px;">
                            请扫描教室内的签到二维码，或使用手机摄像头扫描
                        </p>
                        <button class="btn btn-primary" style="margin-right: 10px;">
                            <i class="fas fa-camera"></i> 开启摄像头
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-upload"></i> 上传二维码图片
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('学生管理 > 学生考勤系统');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false });
            const dateStr = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            document.getElementById('current-time').textContent = timeStr;
            document.getElementById('current-date').textContent = dateStr;
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();

        // 签到方法
        function faceCheckin() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('人脸识别签到成功！', 'success');
            }, 2000);
        }

        function cardCheckin() {
            SmartCampus.showMessage('请将校园卡放在读卡器上', 'info');
        }

        function qrCheckin() {
            document.querySelector('[data-tab="qr-scanner"]').click();
        }

        function gpsCheckin() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('GPS定位签到成功！当前位置：教学楼A座', 'success');
            }, 1500);
        }

        // 请假申请提交
        document.querySelector('.leave-form .btn-success').addEventListener('click', function() {
            SmartCampus.showLoading();
            setTimeout(() => {
                SmartCampus.hideLoading();
                SmartCampus.showMessage('请假申请提交成功，等待审批', 'success');
            }, 1000);
        });

        // 日历日期点击
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.addEventListener('click', function() {
                if (this.classList.contains('has-record') || this.classList.contains('has-absence')) {
                    const dayNum = this.querySelector('.day-number').textContent;
                    SmartCampus.showMessage(`查看${dayNum}日的详细考勤记录`, 'info');
                }
            });
        });
    </script>
</body>
</html>
