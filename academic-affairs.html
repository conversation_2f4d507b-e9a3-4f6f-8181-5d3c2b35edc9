<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教务综合管理 - 智慧校园管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .management-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #3498db;
            background: linear-gradient(135deg, rgba(52,152,219,0.1), rgba(46,204,113,0.1));
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .student-search {
            display: flex;
            gap: 15px;
            align-items: center;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
        }

        .student-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .student-table th {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }

        .student-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .student-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-graduated {
            background: #cce5ff;
            color: #004085;
        }

        .status-suspended {
            background: #fff3cd;
            color: #856404;
        }

        .grade-input-form {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .grade-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .grade-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: 500;
            font-size: 14px;
        }

        .grade-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .grade-input {
            width: 60px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }

        .grade-excellent {
            background: #d4edda;
            color: #155724;
            font-weight: bold;
        }

        .grade-good {
            background: #cce5ff;
            color: #004085;
        }

        .grade-average {
            background: #fff3cd;
            color: #856404;
        }

        .grade-poor {
            background: #f8d7da;
            color: #721c24;
        }

        .exam-schedule {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .exam-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
        }

        .exam-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .exam-info {
            color: #666;
            margin-bottom: 8px;
        }

        .exam-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-top: 10px;
        }

        .exam-upcoming {
            background: #fff3cd;
            color: #856404;
        }

        .exam-ongoing {
            background: #d4edda;
            color: #155724;
        }

        .exam-completed {
            background: #e2e3e5;
            color: #495057;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-content">
            <div class="page-content">
                <h1 class="page-title">
                    <i class="fas fa-clipboard-list"></i> 教务综合管理
                </h1>

                <!-- 功能选项卡 -->
                <div class="management-tabs">
                    <button class="tab-btn active" data-tab="student-records">
                        <i class="fas fa-user-graduate"></i> 学籍管理
                    </button>
                    <button class="tab-btn" data-tab="grade-management">
                        <i class="fas fa-chart-line"></i> 成绩管理
                    </button>
                    <button class="tab-btn" data-tab="exam-management">
                        <i class="fas fa-clipboard-check"></i> 考务管理
                    </button>
                </div>

                <!-- 学籍管理 -->
                <div class="tab-content active" id="student-records">
                    <div class="student-search">
                        <input type="text" class="search-input" placeholder="搜索学生姓名、学号或专业...">
                        <select class="form-control" style="width: 150px;">
                            <option>全部年级</option>
                            <option>2024级</option>
                            <option>2023级</option>
                            <option>2022级</option>
                            <option>2021级</option>
                        </select>
                        <select class="form-control" style="width: 150px;">
                            <option>全部专业</option>
                            <option>计算机科学</option>
                            <option>软件工程</option>
                            <option>电子信息</option>
                            <option>机械工程</option>
                        </select>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-plus"></i> 新增学生
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">学生档案管理</h3>
                        </div>
                        <div class="card-body">
                            <table class="student-table">
                                <thead>
                                    <tr>
                                        <th>学号</th>
                                        <th>姓名</th>
                                        <th>性别</th>
                                        <th>专业</th>
                                        <th>年级</th>
                                        <th>班级</th>
                                        <th>状态</th>
                                        <th>入学时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024001001</td>
                                        <td>张三</td>
                                        <td>男</td>
                                        <td>计算机科学与技术</td>
                                        <td>2024级</td>
                                        <td>计科1班</td>
                                        <td><span class="status-badge status-active">在读</span></td>
                                        <td>2024-09-01</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2023002015</td>
                                        <td>李四</td>
                                        <td>女</td>
                                        <td>软件工程</td>
                                        <td>2023级</td>
                                        <td>软工2班</td>
                                        <td><span class="status-badge status-active">在读</span></td>
                                        <td>2023-09-01</td>
                                        <td>
                                            <button class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2021003028</td>
                                        <td>王五</td>
                                        <td>男</td>
                                        <td>电子信息工程</td>
                                        <td>2021级</td>
                                        <td>电信1班</td>
                                        <td><span class="status-badge status-graduated">已毕业</span></td>
                                        <td>2021-09-01</td>
                                        <td>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2022004012</td>
                                        <td>赵六</td>
                                        <td>女</td>
                                        <td>机械工程</td>
                                        <td>2022级</td>
                                        <td>机械3班</td>
                                        <td><span class="status-badge status-suspended">休学</span></td>
                                        <td>2022-09-01</td>
                                        <td>
                                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">复学</button>
                                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">详情</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 成绩管理 -->
                <div class="tab-content" id="grade-management">
                    <div class="grade-input-form">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-edit"></i> 成绩录入
                        </h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">课程名称</label>
                                <select class="form-control">
                                    <option>选择课程</option>
                                    <option>高等数学</option>
                                    <option>大学英语</option>
                                    <option>计算机基础</option>
                                    <option>数据结构</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">考试类型</label>
                                <select class="form-control">
                                    <option>期末考试</option>
                                    <option>期中考试</option>
                                    <option>平时成绩</option>
                                    <option>实验成绩</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">班级</label>
                                <select class="form-control">
                                    <option>选择班级</option>
                                    <option>计科1班</option>
                                    <option>软工2班</option>
                                    <option>电信1班</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">学期</label>
                                <select class="form-control">
                                    <option>2024春季学期</option>
                                    <option>2023秋季学期</option>
                                    <option>2023春季学期</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i> 查询学生名单
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-file-import"></i> 批量导入
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">高等数学 - 计科1班 - 期末成绩</h3>
                        </div>
                        <div class="card-body">
                            <table class="grade-table">
                                <thead>
                                    <tr>
                                        <th>学号</th>
                                        <th>姓名</th>
                                        <th>平时成绩<br>(30%)</th>
                                        <th>期中成绩<br>(30%)</th>
                                        <th>期末成绩<br>(40%)</th>
                                        <th>总评成绩</th>
                                        <th>等级</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024001001</td>
                                        <td>张三</td>
                                        <td><input type="number" class="grade-input" value="85" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="88" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="92" min="0" max="100"></td>
                                        <td class="grade-excellent">88.6</td>
                                        <td>优秀</td>
                                        <td>已录入</td>
                                    </tr>
                                    <tr>
                                        <td>2024001002</td>
                                        <td>李四</td>
                                        <td><input type="number" class="grade-input" value="78" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="82" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="85" min="0" max="100"></td>
                                        <td class="grade-good">81.9</td>
                                        <td>良好</td>
                                        <td>已录入</td>
                                    </tr>
                                    <tr>
                                        <td>2024001003</td>
                                        <td>王五</td>
                                        <td><input type="number" class="grade-input" value="72" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="75" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="" min="0" max="100" placeholder="待录入"></td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>待录入</td>
                                    </tr>
                                    <tr>
                                        <td>2024001004</td>
                                        <td>赵六</td>
                                        <td><input type="number" class="grade-input" value="65" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="58" min="0" max="100"></td>
                                        <td><input type="number" class="grade-input" value="62" min="0" max="100"></td>
                                        <td class="grade-poor">61.4</td>
                                        <td>不及格</td>
                                        <td>需补考</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div style="margin-top: 20px; text-align: right;">
                                <button class="btn btn-success">
                                    <i class="fas fa-save"></i> 保存成绩
                                </button>
                                <button class="btn btn-primary">
                                    <i class="fas fa-chart-bar"></i> 成绩分析
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 成绩统计 -->
                    <div class="statistics-grid">
                        <div class="stat-card">
                            <div class="stat-number" style="color: #2ecc71;">92.5%</div>
                            <div class="stat-label">及格率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" style="color: #3498db;">78.6</div>
                            <div class="stat-label">平均分</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" style="color: #f39c12;">15.8%</div>
                            <div class="stat-label">优秀率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" style="color: #e74c3c;">3</div>
                            <div class="stat-label">不及格人数</div>
                        </div>
                    </div>
                </div>

                <!-- 考务管理 -->
                <div class="tab-content" id="exam-management">
                    <div class="action-buttons">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新建考试
                        </button>
                        <button class="btn btn-success">
                            <i class="fas fa-calendar-alt"></i> 考试排程
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-map-marker-alt"></i> 考场安排
                        </button>
                    </div>

                    <div class="exam-schedule">
                        <div class="exam-card">
                            <div class="exam-title">高等数学期末考试</div>
                            <div class="exam-info">
                                <i class="fas fa-calendar"></i> 2024年6月20日 09:00-11:00
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-map-marker-alt"></i> 教学楼A座 A101-A105
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-users"></i> 参考人数: 245人
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-user-tie"></i> 监考老师: 张教授、李老师
                            </div>
                            <span class="exam-status exam-upcoming">即将开始</span>
                        </div>

                        <div class="exam-card">
                            <div class="exam-title">大学英语四级模拟</div>
                            <div class="exam-info">
                                <i class="fas fa-calendar"></i> 2024年6月18日 14:30-16:30
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-map-marker-alt"></i> 教学楼B座 B201-B210
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-users"></i> 参考人数: 180人
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-user-tie"></i> 监考老师: 王老师、陈老师
                            </div>
                            <span class="exam-status exam-ongoing">进行中</span>
                        </div>

                        <div class="exam-card">
                            <div class="exam-title">计算机基础期中考试</div>
                            <div class="exam-info">
                                <i class="fas fa-calendar"></i> 2024年6月15日 10:00-12:00
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-map-marker-alt"></i> 实验楼C座 C301-C305
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-users"></i> 参考人数: 156人
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-chart-bar"></i> 平均分: 82.5分
                            </div>
                            <span class="exam-status exam-completed">已完成</span>
                        </div>

                        <div class="exam-card">
                            <div class="exam-title">数据结构与算法</div>
                            <div class="exam-info">
                                <i class="fas fa-calendar"></i> 2024年6月22日 15:00-17:00
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-map-marker-alt"></i> 教学楼A座 A201-A205
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-users"></i> 参考人数: 198人
                            </div>
                            <div class="exam-info">
                                <i class="fas fa-user-tie"></i> 监考老师: 待安排
                            </div>
                            <span class="exam-status exam-upcoming">待安排</span>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3 style="margin-bottom: 20px;">
                            <i class="fas fa-chart-line"></i> 考试成绩分布统计
                        </h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area" style="font-size: 48px; opacity: 0.3;"></i>
                            <span style="margin-left: 15px;">成绩分布图表加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/common.js"></script>
    <script>
        // 初始化页面
        SmartCampus.initializePage('教学管理 > 教务综合管理');

        // 选项卡切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // 成绩输入自动计算
        document.querySelectorAll('.grade-input').forEach(input => {
            input.addEventListener('input', function() {
                const row = this.closest('tr');
                const inputs = row.querySelectorAll('.grade-input');
                const totalCell = row.querySelector('td:nth-child(6)');
                
                if (inputs[0].value && inputs[1].value && inputs[2].value) {
                    const total = (parseFloat(inputs[0].value) * 0.3 + 
                                  parseFloat(inputs[1].value) * 0.3 + 
                                  parseFloat(inputs[2].value) * 0.4).toFixed(1);
                    totalCell.textContent = total;
                    
                    // 设置等级样式
                    totalCell.className = '';
                    if (total >= 90) totalCell.className = 'grade-excellent';
                    else if (total >= 80) totalCell.className = 'grade-good';
                    else if (total >= 70) totalCell.className = 'grade-average';
                    else if (total >= 60) totalCell.className = 'grade-average';
                    else totalCell.className = 'grade-poor';
                }
            });
        });

        // 批量导入成绩
        document.querySelector('[data-tab="grade-management"] .btn-success').addEventListener('click', function() {
            SmartCampus.showMessage('请选择Excel文件进行批量导入', 'info');
        });
    </script>
</body>
</html>
